/**
 * Fetch Query 291 to understand what it does
 */

const https = require('https');
const fs = require('fs');

const METABASE_URL = 'metabase.equalcollective.com';
const API_KEY = 'mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00=';

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: METABASE_URL,
      path: path,
      method: 'GET',
      headers: {
        'X-API-KEY': API_KEY
      }
    };
    
    https.get(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          reject(e);
        }
      });
    }).on('error', reject);
  });
}

async function analyzeQuery291() {
  console.log('Fetching Query 291...\n');
  
  try {
    const query = await makeRequest('/api/card/291');
    
    console.log('Query Name:', query.name);
    console.log('Description:', query.description || 'None');
    console.log('Type:', query.query_type);
    console.log('Collection:', query.collection?.name || 'Root');
    console.log('Created:', query.created_at);
    console.log('Updated:', query.updated_at);
    console.log();
    
    // Check dependencies
    if (query.dataset_query?.query?.['source-table']) {
      const sourceTable = query.dataset_query.query['source-table'];
      console.log('Source:', sourceTable);
      
      // If it's a card reference (starts with "card__")
      if (typeof sourceTable === 'string' && sourceTable.startsWith('card__')) {
        const dependencyId = sourceTable.replace('card__', '');
        console.log(`\nDepends on Query ${dependencyId}`);
        
        // Fetch dependency
        const dependency = await makeRequest(`/api/card/${dependencyId}`);
        console.log(`  → ${dependency.name}`);
      }
    }
    
    // Check if it's native SQL
    if (query.dataset_query?.native?.query) {
      const sql = query.dataset_query.native.query;
      console.log('\nSQL Query Length:', sql.length, 'characters');
      
      // Save SQL
      fs.writeFileSync('query_291.sql', sql);
      console.log('✅ SQL saved to query_291.sql');
      
      // Look for table references
      const tables = new Set();
      const patterns = [
        /FROM\s+"?public2"?\."?(\w+)"?/gi,
        /JOIN\s+"?public2"?\."?(\w+)"?/gi,
        /FROM\s+"?(\w+)"?/gi,
        /JOIN\s+"?(\w+)"?/gi
      ];
      
      patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(sql)) !== null) {
          if (match[1] && !['SELECT', 'FROM', 'WHERE', 'AND', 'OR', 'ON', 'AS'].includes(match[1].toUpperCase())) {
            tables.add(match[1]);
          }
        }
      });
      
      console.log('\nTables referenced:');
      Array.from(tables).forEach(table => {
        console.log('  -', table);
      });
      
      // Check for timeout-prone operations
      console.log('\n=== PERFORMANCE ANALYSIS ===');
      
      if (sql.includes('FunnelClientAnalytics')) {
        console.log('⚠️  Uses FunnelClientAnalytics table');
      }
      
      const hasAggregation = /GROUP BY|SUM|COUNT|AVG|MAX|MIN/i.test(sql);
      const hasJoins = /JOIN/i.test(sql);
      const hasCTE = /WITH\s+\w+\s+AS/i.test(sql);
      
      console.log('Has aggregation:', hasAggregation);
      console.log('Has joins:', hasJoins);
      console.log('Has CTEs:', hasCTE);
      
      // Check for date ranges
      if (/date|time|week|month/i.test(sql)) {
        console.log('⚠️  Has date/time operations (potential performance impact)');
      }
    }
    
    // Save full query details
    fs.writeFileSync('query_291_full.json', JSON.stringify(query, null, 2));
    console.log('\n✅ Full query details saved to query_291_full.json');
    
    // Now let's check what FunnelClientAnalytics creation does
    console.log('\n=== CHECKING FUNNEL CLIENT ANALYTICS CREATION ===\n');
    console.log('Query 291 is likely used to create FunnelClientAnalytics table.');
    console.log('If Query 291 times out, FunnelClientAnalytics creation will fail.');
    console.log('\nPossible solutions:');
    console.log('1. Create our own aggregation directly from base tables');
    console.log('2. Add timeout handling and retry logic');
    console.log('3. Create smaller, incremental aggregations');
    console.log('4. Use a different approach that bypasses Query 291');
    
  } catch (error) {
    console.error('Error fetching query:', error.message);
  }
}

analyzeQuery291();