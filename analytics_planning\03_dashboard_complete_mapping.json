{"dashboard": {"id": 2, "name": "Old Jeff", "collection": "Our analytics"}, "filters": [{"id": "d3afd38c", "name": "Tags", "slug": "tags", "type": "string/=", "mappings": []}, {"id": "ae08f588", "name": "Clients", "slug": "clients", "type": "string/=", "mappings": []}, {"id": "6511d925", "name": "Funnel", "slug": "funnel", "type": "string/=", "mappings": []}, {"id": "ddca8a75", "name": "Provider", "slug": "provider", "type": "string/=", "mappings": []}], "queries": [{"id": 112, "name": "Total Prospects in System with eMails (without SP9)", "display": "object", "query_type": "query", "collection": "Overall Goals", "tables": [], "sql": null, "source_query": 117, "aggregations": [["count"], ["distinct", ["field", "amazon_seller_id", {"base-type": "type/Text"}]]], "filters": ["and", ["!=", ["field", "jeff_search_priority", {"base-type": "type/Text"}], "SP9"], ["in", ["field", "email_status", {"base-type": "type/Text"}], "VERIFIED", "CATCHALL", "GREYLISTING"]], "breakouts": []}, {"id": 113, "name": "Numbers of Sellers with a Correct Website (without SP9)", "display": "progress", "query_type": "query", "collection": "Overall Goals", "tables": [], "sql": null, "source_query": 119, "aggregations": [["count"]], "filters": ["and", ["=", ["field", "website_status", {"base-type": "type/Text"}], "Final Correct"], ["!=", ["field", "jeff_search_priority", {"base-type": "type/Text"}], "SP9"]], "breakouts": []}, {"id": 125, "name": "Apollo Website Search Funnel Analysis", "display": "table", "query_type": "native", "collection": "Aggregates", "tables": [{"name": "status_options", "schema": "public"}, {"name": "public", "schema": "public"}, {"name": "status_counts", "schema": "public"}, {"name": "all_combinations", "schema": "public"}, {"name": "priority_options", "schema": "public"}, {"name": "actual_counts", "schema": "public"}], "sql": "-- First, get all possible status values\nWITH status_options AS (\n    SELECT '1 - Eligible' AS status, 1 AS sort_order\n    UNION ALL SELECT '2 - Pass' AS status, 2 AS sort_order\n    UNION ALL SELECT '3 - Fail' AS status, 3 AS sort_order\n    UNION ALL SELECT '4 - Success' AS status, 4 AS sort_order\n    UNION ALL SELECT '5 - Missed' AS status, 5 AS sort_order\n),\n\n-- Get a distinct list of all jeff_search_priority values\npriority_options AS (\n    SELECT DISTINCT jeff_search_priority\n    FROM {{#119-sellers-with-search-priority}}\n),\n\n-- Create all possible combinations of status and priority\nall_combinations AS (\n    SELECT \n        so.status,\n        so.sort_order,\n        po.jeff_search_priority\n    FROM status_options so\n    CROSS JOIN priority_options po\n),\n\n-- Original query to get actual counts\nstatus_counts AS (\n    -- Eligible status\n    SELECT \n        '1 - Eligible' AS status,\n        1 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status != 'Final Correct'\n      AND NOT (c.lookup_sources @> '[\"WEBSITE_SEARCH_APOLLO_P1\"]')\n    \n    UNION ALL\n    \n    -- Pass status\n    SELECT \n        '2 - Pass' AS status,\n        2 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_APOLLO_P1\"]'\n    \n    UNION ALL\n    \n    -- Fail status\n    SELECT \n        '3 - Fail' AS status,\n        3 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_APOLLO_P1:fail\"]'\n    \n    UNION ALL\n    \n    -- Success status\n    SELECT \n        '4 - Success' AS status,\n        4 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_APOLLO_P1:success\"]'\n    \n    UNION ALL\n    \n    -- Missed status: In Pass but not in Fail or Success\n    SELECT \n        '5 - Missed' AS status,\n        5 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_APOLLO_P1\"]'  -- Has Pass condition\n      AND NOT (c.lookup_sources @> '[\"WEBSITE_SEARCH_APOLLO_P1:fail\"]')  -- Not in Fail\n      AND NOT (c.lookup_sources @> '[\"WEBSITE_SEARCH_APOLLO_P1:success\"]')  -- Not in Success\n),\n\n-- Aggregate the actual counts\nactual_counts AS (\n    SELECT \n        sc.status,\n        sc.sort_order,\n        sp.jeff_search_priority,\n        COUNT(*) AS count\n    FROM status_counts sc\n    JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n    GROUP BY \n        sc.status, \n        sc.sort_order,\n        sp.jeff_search_priority\n)\n\n-- Final query with 0 counts for missing combinations\nSELECT \n    ac.status,\n    ac.jeff_search_priority,\n    ac.sort_order,\n    COALESCE(actual.count, 0) AS count  -- Use 0 instead of NULL for missing combinations\nFROM all_combinations ac\nLEFT JOIN actual_counts actual ON \n    ac.status = actual.status AND\n    ac.jeff_search_priority = actual.jeff_search_priority\nORDER BY \n    ac.sort_order,\n    ac.jeff_search_priority;\n\n-- -- First, get all possible status values\n\t-- WITH status_options AS (\n\t--     SELECT '1 - Eligible' AS status, 1 AS sort_order\n\t--     UNION ALL SELECT '2 - Pass' AS status, 2 AS sort_order\n\t--     UNION ALL SELECT '3 - Fail' AS status, 3 AS sort_order\n\t--     UNION ALL SELECT '4 - Success' AS status, 4 AS sort_order\n\t-- ),\n\t\n\t-- -- Get a distinct list of all jeff_search_priority values\n\t-- priority_options AS (\n\t--     SELECT DISTINCT jeff_search_priority\n\t--     FROM {{#119-sellers-with-search-priority}}\n\t-- ),\n\t\n\t-- -- Create all possible combinations of status and priority\n\t-- all_combinations AS (\n\t--     SELECT \n\t--         so.status,\n\t--         so.sort_order,\n\t--         po.jeff_search_priority\n\t--     FROM status_options so\n\t--     CROSS JOIN priority_options po\n\t-- ),\n\t\n\t-- -- Original query to get actual counts\n\t-- status_counts AS (\n\t--     -- Eligible status\n\t--     SELECT \n\t--         '1 - Eligible' AS status,\n\t--         1 AS sort_order,\n\t--         c.id AS company_id\n\t--     FROM public.\"Company\" c\n\t--     WHERE c.website_status != 'Final Correct'\n\t--       AND NOT (c.lookup_sources @> '[\"WEBSITE_SEARCH_APOLLO_P1\"]')\n\t    \n\t--     UNION ALL\n\t    \n\t--     -- Pass status\n\t--     SELECT \n\t--         '2 - Pass' AS status,\n\t--         2 AS sort_order,\n\t--         c.id AS company_id\n\t--     FROM public.\"Company\" c\n\t--     WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_APOLLO_P1\"]'\n\t    \n\t--     UNION ALL\n\t    \n\t--     -- Fail status\n\t--     SELECT \n\t--         '3 - Fail' AS status,\n\t--         3 AS sort_order,\n\t--         c.id AS company_id\n\t--     FROM public.\"Company\" c\n\t--     WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_APOLLO_P1:fail\"]'\n\t    \n\t--     UNION ALL\n\t    \n\t--     -- Success status\n\t--     SELECT \n\t--         '4 - Success' AS status,\n\t--         4 AS sort_order,\n\t--         c.id AS company_id\n\t--     FROM public.\"Company\" c\n\t--     WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_APOLLO_P1:success\"]'\n\t-- ),\n\t\n\t-- -- Aggregate the actual counts\n\t-- actual_counts AS (\n\t--     SELECT \n\t--         sc.status,\n\t--         sc.sort_order,\n\t--         sp.jeff_search_priority,\n\t--         COUNT(*) AS count\n\t--     FROM status_counts sc\n\t--     JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n\t--     GROUP BY \n\t--         sc.status, \n\t--         sc.sort_order,\n\t--         sp.jeff_search_priority\n\t-- )\n\t\n\t-- -- Final query with 0 counts for missing combinations\n\t-- SELECT \n\t--     ac.status,\n\t--     ac.jeff_search_priority,\n\t--     ac.sort_order,\n\t--     COALESCE(actual.count, 0) AS count  -- Use 0 instead of NULL for missing combinations\n\t-- FROM all_combinations ac\n\t-- LEFT JOIN actual_counts actual ON \n\t--     ac.status = actual.status AND\n\t--     ac.jeff_search_priority = actual.jeff_search_priority\n\t-- ORDER BY \n\t--     ac.sort_order,\n\t--     ac.jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 126, "name": "Equal SERP Funnel Analysis", "display": "table", "query_type": "native", "collection": "Aggregates", "tables": [{"name": "public", "schema": "public"}, {"name": "status_options", "schema": "public"}, {"name": "status_counts", "schema": "public"}, {"name": "all_combinations", "schema": "public"}, {"name": "priority_options", "schema": "public"}, {"name": "actual_counts", "schema": "public"}], "sql": "-- First, get all possible status values\nWITH status_options AS (\n    SELECT '1 - Eligible' AS status, 1 AS sort_order\n    UNION ALL SELECT '2 - Pass' AS status, 2 AS sort_order\n    UNION ALL SELECT '3 - Fail' AS status, 3 AS sort_order\n    UNION ALL SELECT '4 - Success' AS status, 4 AS sort_order\n    UNION ALL SELECT '5 - Missed' AS status, 5 AS sort_order\n),\n\n-- Get the actual data counts\nstatus_counts AS (\n    -- Eligible status - Companies that failed the previous funnel\n    SELECT \n        '1 - Eligible' AS status,\n        1 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_APOLLO_P1:fail\"]'\n\tAND NOT (c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P4\"]')\n\tAND c.website_status != 'Final Correct'\n    \n    UNION ALL\n    \n    -- Pass status - Companies that entered the new funnel\n    SELECT \n        '2 - Pass' AS status,\n        2 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P4\"]'\n    \n    UNION ALL\n    \n    -- Fail status - Companies that failed the new funnel\n    SELECT \n        '3 - Fail' AS status,\n        3 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P4:fail\"]'\n    \n    UNION ALL\n    \n    -- Success status - Companies that succeeded in the new funnel\n    SELECT \n        '4 - Success' AS status,\n        4 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P4:success\"]'\n    \n    UNION ALL\n    \n    -- Missed status - Companies in Pass but not in Fail or Success\n    SELECT \n        '5 - Missed' AS status,\n        5 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P4\"]'  -- Has Pass condition\n      AND NOT (c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P4:fail\"]')  -- Not in Fail\n      AND NOT (c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P4:success\"]')  -- Not in Success\n),\n\n-- Get a distinct list of all jeff_search_priority values\npriority_options AS (\n    SELECT DISTINCT jeff_search_priority\n    FROM {{#119-sellers-with-search-priority}}\n),\n\n-- Create all possible combinations of status and priority\nall_combinations AS (\n    SELECT \n        so.status,\n        so.sort_order,\n        po.jeff_search_priority\n    FROM status_options so\n    CROSS JOIN priority_options po\n),\n\n-- Count actual occurrences\nactual_counts AS (\n    SELECT \n        sc.status,\n        sc.sort_order,\n        sp.jeff_search_priority,\n        COUNT(*) AS count\n    FROM status_counts sc\n    JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n    GROUP BY \n        sc.status, \n        sc.sort_order,\n        sp.jeff_search_priority\n)\n\n-- Final query with zeros for missing combinations\nSELECT \n    ac.status,\n    ac.jeff_search_priority,\n    ac.sort_order,\n    COALESCE(actual.count, 0) AS count  -- Replace NULL with 0 for missing combinations\nFROM all_combinations ac\nLEFT JOIN actual_counts actual ON \n    ac.status = actual.status AND\n    ac.jeff_search_priority = actual.jeff_search_priority\nORDER BY \n    ac.sort_order,\n    ac.jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 130, "name": "Apollo Prospect Search Funnel 1st Pass", "display": "table", "query_type": "native", "collection": "Aggregates", "tables": [{"name": "status_options", "schema": "public"}, {"name": "public", "schema": "public"}, {"name": "companies_with_apollo_prospects", "schema": "public"}, {"name": "status_counts", "schema": "public"}, {"name": "all_combinations", "schema": "public"}, {"name": "priority_options", "schema": "public"}, {"name": "actual_counts", "schema": "public"}], "sql": "-- First, get all possible status values\nWITH status_options AS (\n    SELECT '1 - Eligible' AS status, 1 AS sort_order\n    UNION ALL SELECT '2 - Passed' AS status, 2 AS sort_order\n    UNION ALL SELECT '3 - Failed' AS status, 3 AS sort_order\n    UNION ALL SELECT '4 - Success' AS status, 4 AS sort_order\n),\n\n-- Get a distinct list of all jeff_search_priority values\npriority_options AS (\n    SELECT DISTINCT jeff_search_priority\n    FROM {{#119-sellers-with-search-priority}}\n),\n\n-- Create all possible combinations of status and priority\nall_combinations AS (\n    SELECT \n        so.status,\n        so.sort_order,\n        po.jeff_search_priority\n    FROM status_options so\n    CROSS JOIN priority_options po\n),\n\n-- Companies with APOLLO in prospect sources (SUCCESS category)\ncompanies_with_apollo_prospects AS (\n    SELECT DISTINCT\n        c.id\n    FROM public.\"Company\" c\n    JOIN public.\"Prospect\" p ON c.id = p.company_id\n    WHERE p.sources::jsonb @> '[\"PROSPECT_SEARCH_APOLLO_STEP1_P1\"]'\n      AND c.website_status = 'Final Correct'  -- Only Final Correct\n),\n\n-- Original query to get actual counts with MUTUALLY EXCLUSIVE categories\nstatus_counts AS (\n    -- Success status (highest priority): Final Correct companies with 1+ prospects with APOLLO in sources\n    SELECT \n        '4 - Success' AS status,\n        4 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status = 'Final Correct'\n      AND c.id IN (SELECT id FROM companies_with_apollo_prospects)\n    \n    UNION ALL\n    \n    -- Failed status: Final Correct companies with APOLLO in lookup_sources but no prospects with APOLLO\n    SELECT \n        '3 - Failed' AS status,\n        3 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status = 'Final Correct'\n      AND c.lookup_sources @> '[\"PROSPECT_SEARCH_APOLLO_STEP1_P1\"]'\n      AND c.id NOT IN (SELECT id FROM companies_with_apollo_prospects)\n    \n    UNION ALL\n    \n    -- Eligible status: Final Correct companies without APOLLO in lookup_sources\n    SELECT \n        '1 - Eligible' AS status,\n        1 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status = 'Final Correct'\n      AND NOT (c.lookup_sources @> '[\"PROSPECT_SEARCH_APOLLO_STEP1_P1\"]')\n      AND c.id NOT IN (SELECT id FROM companies_with_apollo_prospects)\n),\n\n-- Aggregate the actual counts\nactual_counts AS (\n    SELECT \n        sc.status,\n        sc.sort_order,\n        sp.jeff_search_priority,\n        COUNT(*) AS count\n    FROM status_counts sc\n    JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n    GROUP BY \n        sc.status, \n        sc.sort_order,\n        sp.jeff_search_priority\n)\n\n-- Final query with NULL counts for missing combinations\nSELECT \n    ac.status,\n    ac.jeff_search_priority,\n    COALESCE(actual.count, 0) AS count  -- Use 0 instead of NULL for missing combinations\nFROM all_combinations ac\nLEFT JOIN actual_counts actual ON \n    ac.status = actual.status AND\n    ac.jeff_search_priority = actual.jeff_search_priority\nORDER BY \n    ac.sort_order,\n    ac.jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 132, "name": "UNAVAILABLE (from Apollo) Prospect Search on FINDYMAIL Funnel", "display": "table", "query_type": "native", "collection": "Aggregates", "tables": [{"name": "status_options", "schema": "public"}, {"name": "public", "schema": "public"}, {"name": "status_counts", "schema": "public"}, {"name": "all_combinations", "schema": "public"}, {"name": "priority_options", "schema": "public"}, {"name": "actual_counts", "schema": "public"}], "sql": "-- First, get all possible status values\nWITH status_options AS (\n    SELECT '1 - Eligible' AS status, 1 AS sort_order\n    UNION ALL SELECT '2 - Pass' AS status, 2 AS sort_order\n    UNION ALL SELECT '3 - Fail' AS status, 3 AS sort_order\n    UNION ALL SELECT '4 - Success' AS status, 4 AS sort_order\n),\n\n-- Get a distinct list of all jeff_search_priority values\npriority_options AS (\n    SELECT DISTINCT jeff_search_priority\n    FROM {{#119-sellers-with-search-priority}}\n),\n\n-- Create all possible combinations of status and priority\nall_combinations AS (\n    SELECT \n        so.status,\n        so.sort_order,\n        po.jeff_search_priority\n    FROM status_options so\n    CROSS JOIN priority_options po\n),\n\n-- Get status counts for prospects with UNAVAILABLE email_status\nstatus_counts AS (\n    -- Eligible status: Prospects with UNAVAILABLE email_status and no FindyMail in sources\n    SELECT \n        '1 - Eligible' AS status,\n        1 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.email_status = 'UNAVAILABLE'\n      AND NOT (p.sources::jsonb @> '[\"PROSPECT_SEARCH_FINDYMAIL_EMAIL_P1\"]')\n    \n    UNION ALL\n    \n    -- Pass status: Prospects with FindyMail in sources\n    SELECT \n        '2 - Pass' AS status,\n        2 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"PROSPECT_SEARCH_FINDYMAIL_EMAIL_P1\"]'\n    \n    UNION ALL\n    \n    -- Fail status: Prospects with FindyMail:fail in sources\n    SELECT \n        '3 - Fail' AS status,\n        3 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"PROSPECT_SEARCH_FINDYMAIL_EMAIL_P1:fail\"]'\n    \n    UNION ALL\n    \n    -- Success status: Prospects with FindyMail:success in sources\n    SELECT \n        '4 - Success' AS status,\n        4 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"PROSPECT_SEARCH_FINDYMAIL_EMAIL_P1:success\"]'\n),\n\n-- Count actual occurrences\nactual_counts AS (\n    SELECT \n        sc.status,\n        sc.sort_order,\n        sp.jeff_search_priority,\n        COUNT(*) AS count\n    FROM status_counts sc\n    JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n    GROUP BY \n        sc.status, \n        sc.sort_order,\n        sp.jeff_search_priority\n)\n\n-- Final query with NULL counts for missing combinations\nSELECT \n    ac.status,\n    ac.jeff_search_priority,\n    -- ac.sort_order,\n    COALESCE(actual.count, 0) AS count  -- Use 0 instead of NULL for missing combinations\nFROM all_combinations ac\nLEFT JOIN actual_counts actual ON \n    ac.status = actual.status AND\n    ac.jeff_search_priority = actual.jeff_search_priority\nORDER BY \n    ac.sort_order,\n    ac.jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 134, "name": "Sellers with Maybe Status", "display": "table", "query_type": "query", "collection": "Aggregates", "tables": [], "sql": null, "source_query": 119, "aggregations": [["count"], ["distinct", ["field", "amazon_seller_id", {"base-type": "type/Text"}]]], "filters": ["and", ["=", ["field", "website_status", {"base-type": "type/Text"}], "Maybe"], ["!=", ["field", "jeff_search_priority", {"base-type": "type/Text"}], "SP9"]], "breakouts": [["field", "jeff_search_priority", {"base-type": "type/Text"}]]}, {"id": 138, "name": "Website eMail Harvesting Funnel", "display": "table", "query_type": "native", "collection": "Aggregates", "tables": [{"name": "status_options", "schema": "public"}, {"name": "public", "schema": "public"}, {"name": "companies_with_harvest_prospects", "schema": "public"}, {"name": "status_counts", "schema": "public"}, {"name": "all_combinations", "schema": "public"}, {"name": "priority_options", "schema": "public"}, {"name": "actual_counts", "schema": "public"}], "sql": "-- First, get all possible status values\nWITH status_options AS (\n    SELECT '1 - Eligible' AS status, 1 AS sort_order\n    UNION ALL SELECT '2 - Passed' AS status, 2 AS sort_order\n    UNION ALL SELECT '3 - Failed' AS status, 3 AS sort_order\n    UNION ALL SELECT '4 - Success' AS status, 4 AS sort_order\n),\n\n-- Get a distinct list of all jeff_search_priority values\npriority_options AS (\n    SELECT DISTINCT jeff_search_priority\n    FROM {{#119-sellers-with-search-priority}}\n),\n\n-- Create all possible combinations of status and priority\nall_combinations AS (\n    SELECT \n        so.status,\n        so.sort_order,\n        po.jeff_search_priority\n    FROM status_options so\n    CROSS JOIN priority_options po\n),\n\n-- Companies with DOMAIN_EMAIL_HARVEST in prospect sources (SUCCESS category)\ncompanies_with_harvest_prospects AS (\n    SELECT DISTINCT\n        c.id\n    FROM public.\"Company\" c\n    JOIN public.\"Prospect\" p ON c.id = p.company_id\n    WHERE p.sources::jsonb @> '[\"PROSPECT_SEARCH_DOMAIN_EMAIL_HARVEST_P1\"]'\n      AND c.website_status = 'Final Correct'  -- Only Final Correct\n),\n\n-- Original query to get actual counts with MUTUALLY EXCLUSIVE categories\nstatus_counts AS (\n    -- Success status (highest priority): Final Correct companies with 1+ prospects with DOMAIN_EMAIL_HARVEST in sources\n    SELECT \n        '4 - Success' AS status,\n        4 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status = 'Final Correct'\n      AND c.id IN (SELECT id FROM companies_with_harvest_prospects)\n    \n    UNION ALL\n    \n    -- Failed status: Final Correct companies with DOMAIN_EMAIL_HARVEST in lookup_sources but no prospects with DOMAIN_EMAIL_HARVEST\n    SELECT \n        '3 - Failed' AS status,\n        3 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status = 'Final Correct'\n      AND c.lookup_sources @> '[\"PROSPECT_SEARCH_DOMAIN_EMAIL_HARVEST_P1\"]'\n      AND c.id NOT IN (SELECT id FROM companies_with_harvest_prospects)\n    \n    UNION ALL\n    \n    -- Eligible status: Final Correct companies without DOMAIN_EMAIL_HARVEST in lookup_sources\n    SELECT \n        '1 - Eligible' AS status,\n        1 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status = 'Final Correct'\n      AND NOT (c.lookup_sources @> '[\"PROSPECT_SEARCH_DOMAIN_EMAIL_HARVEST_P1\"]')\n      AND c.id NOT IN (SELECT id FROM companies_with_harvest_prospects)\n),\n\n-- Aggregate the actual counts\nactual_counts AS (\n    SELECT \n        sc.status,\n        sc.sort_order,\n        sp.jeff_search_priority,\n        COUNT(*) AS count\n    FROM status_counts sc\n    JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n    GROUP BY \n        sc.status, \n        sc.sort_order,\n        sp.jeff_search_priority\n)\n\n-- Final query with NULL counts for missing combinations\nSELECT \n    ac.status,\n    ac.jeff_search_priority,\n    COALESCE(actual.count, 0) AS count  -- Use 0 instead of NULL for missing combinations\nFROM all_combinations ac\nLEFT JOIN actual_counts actual ON \n    ac.status = actual.status AND\n    ac.jeff_search_priority = actual.jeff_search_priority\nORDER BY \n    ac.sort_order,\n    ac.jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 141, "name": "Amazon SF eMail Harvesting Funnel", "display": "table", "query_type": "native", "collection": "Aggregates", "tables": [{"name": "status_options", "schema": "public"}, {"name": "public", "schema": "public"}, {"name": "companies_with_amazon_sf_prospects", "schema": "public"}, {"name": "status_counts", "schema": "public"}, {"name": "all_combinations", "schema": "public"}, {"name": "priority_options", "schema": "public"}, {"name": "actual_counts", "schema": "public"}], "sql": "-- First, get all possible status values\nWITH status_options AS (\n    SELECT '1 - Eligible' AS status, 1 AS sort_order\n    UNION ALL SELECT '2 - Passed' AS status, 2 AS sort_order\n    UNION ALL SELECT '3 - Failed' AS status, 3 AS sort_order\n    UNION ALL SELECT '4 - Success' AS status, 4 AS sort_order\n),\n\n-- Get a distinct list of all jeff_search_priority values\npriority_options AS (\n    SELECT DISTINCT jeff_search_priority\n    FROM {{#119-sellers-with-search-priority}}\n),\n\n-- Create all possible combinations of status and priority\nall_combinations AS (\n    SELECT \n        so.status,\n        so.sort_order,\n        po.jeff_search_priority\n    FROM status_options so\n    CROSS JOIN priority_options po\n),\n\n-- Companies with AMAZON_SF_SCRAPING in prospect sources (SUCCESS category)\ncompanies_with_amazon_sf_prospects AS (\n    SELECT DISTINCT\n        c.id\n    FROM public.\"Company\" c\n    JOIN public.\"Prospect\" p ON c.id = p.company_id\n    WHERE p.sources::jsonb @> '[\"PROSPECT_SEARCH_AMZ_SF_EMAIL_HARVEST_P1\"]'\n),\n\n-- Original query to get actual counts with M<PERSON>UALLY EXCLUSIVE categories\nstatus_counts AS (\n    -- Success status (highest priority): Companies with 1+ prospects with AMAZON_SF_SCRAPING in sources\n    SELECT \n        '4 - Success' AS status,\n        4 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.id IN (SELECT id FROM companies_with_amazon_sf_prospects)\n    \n    UNION ALL\n    \n    -- Failed status: Companies with AMAZON_SF_SCRAPING in lookup_sources but no prospects with AMAZON_SF_SCRAPING\n    SELECT \n        '3 - Failed' AS status,\n        3 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"PROSPECT_SEARCH_AMZ_SF_EMAIL_HARVEST_P1\"]'\n      AND c.id NOT IN (SELECT id FROM companies_with_amazon_sf_prospects)\n    \n    UNION ALL\n    \n    -- Eligible status: Companies without AMAZON_SF_SCRAPING in lookup_sources\n    SELECT \n        '1 - Eligible' AS status,\n        1 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE NOT (c.lookup_sources @> '[\"PROSPECT_SEARCH_AMZ_SF_EMAIL_HARVEST_P1\"]')\n      AND c.id NOT IN (SELECT id FROM companies_with_amazon_sf_prospects)\n),\n\n-- Aggregate the actual counts\nactual_counts AS (\n    SELECT \n        sc.status,\n        sc.sort_order,\n        sp.jeff_search_priority,\n        COUNT(*) AS count\n    FROM status_counts sc\n    JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n    GROUP BY \n        sc.status, \n        sc.sort_order,\n        sp.jeff_search_priority\n)\n\n-- Final query with NULL counts for missing combinations\nSELECT \n    ac.status,\n    ac.jeff_search_priority,\n    COALESCE(actual.count, 0) AS count  -- Use 0 instead of NULL for missing combinations\nFROM all_combinations ac\nLEFT JOIN actual_counts actual ON \n    ac.status = actual.status AND\n    ac.jeff_search_priority = actual.jeff_search_priority\nORDER BY \n    ac.sort_order,\n    ac.jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 143, "name": "eMail Verification Bulk Mail Checker 1st Pass", "display": "table", "query_type": "native", "collection": "Aggregates", "tables": [{"name": "status_options", "schema": "public"}, {"name": "public", "schema": "public"}, {"name": "status_counts", "schema": "public"}, {"name": "all_combinations", "schema": "public"}, {"name": "priority_options", "schema": "public"}, {"name": "actual_counts", "schema": "public"}], "sql": "-- First, get all possible status values\nWITH status_options AS (\n    SELECT '1 - Eligible' AS status, 1 AS sort_order\n    UNION ALL SELECT '2 - Pass' AS status, 2 AS sort_order\n    UNION ALL SELECT '3 - Fail' AS status, 3 AS sort_order\n    UNION ALL SELECT '4 - Inconclusive' AS status, 4 AS sort_order\n    UNION ALL SELECT '5 - Success' AS status, 5 AS sort_order\n    UNION ALL SELECT '6 - Missed' AS status, 6 AS sort_order\n),\n-- Get a distinct list of all jeff_search_priority values\npriority_options AS (\n    SELECT DISTINCT jeff_search_priority\n    FROM {{#119-sellers-with-search-priority}}\n),\n-- Create all possible combinations of status and priority\nall_combinations AS (\n    SELECT \n        so.status,\n        so.sort_order,\n        po.jeff_search_priority\n    FROM status_options so\n    CROSS JOIN priority_options po\n),\n-- Get status counts for prospects with UNAVAILABLE email_status\nstatus_counts AS (\n    -- Eligible status: Prospects with UNVERIFIED email_status and no BULK_EMAIL_CHECKER in sources\n    SELECT \n        '1 - Eligible' AS status,\n        1 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.email_status = 'UNVERIFIED'\n      AND NOT (p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP1_P1\"]')\n    \n    UNION ALL\n    \n    -- Pass status: Prospects with BULK_EMAIL_CHECKER in sources\n    SELECT \n        '2 - Pass' AS status,\n        2 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP1_P1\"]'\n    \n    UNION ALL\n    \n    -- Fail status: Prospects with BULK_EMAIL_CHECKER:fail in sources\n    SELECT \n        '3 - Fail' AS status,\n        3 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:fail\"]'\n    \n    UNION ALL\n    \n    -- Inconclusive status: Prospects with BULK_EMAIL_CHECKER:inconclusive in sources AND email_status INCONCLUSIVE\n    SELECT \n        '4 - Inconclusive' AS status,\n        4 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:inconclusive\"]'\n      -- AND p.email_status = 'INCONCLUSIVE'\n    \n    UNION ALL\n    \n    -- Success status: Prospects with BULK_EMAIL_CHECKER:success in sources\n    SELECT \n        '5 - Success' AS status,\n        5 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:success\"]'\n    \n    UNION ALL\n    \n    -- Missed status: Prospects in Pass but not in Fail, Inconclusive, or Success\n    SELECT \n        '6 - Missed' AS status,\n        6 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP1_P1\"]'  -- Has Pass condition\n      AND NOT (p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:fail\"]')  -- Not in Fail\n      AND NOT (p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:inconclusive\"]')  -- Not in Inconclusive\n      AND NOT (p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:success\"]')  -- Not in Success\n),\n-- Count actual occurrences\nactual_counts AS (\n    SELECT \n        sc.status,\n        sc.sort_order,\n        sp.jeff_search_priority,\n        COUNT(*) AS count\n    FROM status_counts sc\n    JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n    GROUP BY \n        sc.status, \n        sc.sort_order,\n        sp.jeff_search_priority\n)\n-- Final query with NULL counts for missing combinations\nSELECT \n    ac.status,\n    ac.jeff_search_priority,\n    COALESCE(actual.count, 0) AS count  -- Use 0 instead of NULL for missing combinations\nFROM all_combinations ac\nLEFT JOIN actual_counts actual ON \n    ac.status = actual.status AND\n    ac.jeff_search_priority = actual.jeff_search_priority\nORDER BY \n    ac.sort_order,\n    ac.jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 148, "name": "Sellers with Websites Found using Storeleads", "display": "bar", "query_type": "native", "collection": "Data Sanity & Composite Metrics", "tables": [], "sql": "SELECT \n    jeff_search_priority,\n    COUNT(*) AS seller_count\nFROM {{#119-sellers-with-search-priority}}\nWHERE lookup_sources @> '[\"STORELEADS_P1:success\"]'\nGROUP BY jeff_search_priority\nORDER BY jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 149, "name": "Sellers with Correct Websites & No Prospects", "display": "pie", "query_type": "query", "collection": "Data Sanity & Composite Metrics", "tables": [], "sql": null, "source_query": 119, "aggregations": [["count"]], "filters": ["and", ["=", ["field", "website_status", {"base-type": "type/Text"}], "Final Correct"], ["contains", ["field", "jeff_send_priority", {"base-type": "type/Text"}], ":0", {"case-sensitive": false}], ["!=", ["field", "jeff_search_priority", {"base-type": "type/Text"}], "SP9"], ["=", ["field", "jeff_num_unverified", {"base-type": "type/BigInteger"}], 0]], "breakouts": [["field", "jeff_search_priority", {"base-type": "type/Text"}]]}, {"id": 150, "name": "Final Correct Status", "display": "pivot", "query_type": "query", "collection": "Data Sanity & Composite Metrics", "tables": [], "sql": null, "source_query": 119, "aggregations": [["count"]], "filters": [], "breakouts": [["field", "jeff_search_priority", {"base-type": "type/Text"}], ["expression", "Final Website Status", {"base-type": "type/Text"}]]}, {"id": 152, "name": "# of Unverified eMail Status Prospects (without SP9)", "display": "scalar", "query_type": "query", "collection": "Overall Goals", "tables": [], "sql": null, "source_query": 117, "aggregations": [["count"]], "filters": ["and", ["!=", ["field", "jeff_search_priority", {"base-type": "type/Text"}], "SP9"], ["=", ["field", "email_status", {"base-type": "type/Text"}], "UNVERIFIED"]], "breakouts": []}, {"id": 154, "name": "eMail Verification Bulk Mail Checker 2nd Pass", "display": "table", "query_type": "native", "collection": "Aggregates", "tables": [{"name": "status_options", "schema": "public"}, {"name": "public", "schema": "public"}, {"name": "Phase", "schema": "public"}, {"name": "status_counts", "schema": "public"}, {"name": "all_combinations", "schema": "public"}, {"name": "priority_options", "schema": "public"}, {"name": "actual_counts", "schema": "public"}], "sql": "-- First, get all possible status values\nWITH status_options AS (\n    SELECT '1 - Eligible' AS status, 1 AS sort_order\n    UNION ALL SELECT '2 - Pass' AS status, 2 AS sort_order\n    UNION ALL SELECT '3 - Fail' AS status, 3 AS sort_order\n    UNION ALL SELECT '4 - Inconclusive' AS status, 4 AS sort_order\n    UNION ALL SELECT '5 - Success' AS status, 5 AS sort_order\n    UNION ALL SELECT '6 - Missed' AS status, 6 AS sort_order\n),\n-- Get a distinct list of all jeff_search_priority values\npriority_options AS (\n    SELECT DISTINCT jeff_search_priority\n    FROM {{#119-sellers-with-search-priority}}\n),\n-- Create all possible combinations of status and priority\nall_combinations AS (\n    SELECT \n        so.status,\n        so.sort_order,\n        po.jeff_search_priority\n    FROM status_options so\n    CROSS JOIN priority_options po\n),\n-- Get status counts for prospects with UNAVAILABLE email_status\nstatus_counts AS (\n    -- Eligible status for Phase 2: Prospects that were either FAIL or INCONCLUSIVE in Phase 1\n    SELECT \n        '1 - Eligible' AS status,\n        1 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE (\n          -- Fail condition from Phase 1\n          (p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:fail\"]')\n          OR\n          -- Inconclusive condition from Phase 1  \n          (p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:inconclusive\"]' AND p.email_status = 'INCONCLUSIVE')\n        )\n        -- Not yet in Phase 2\n        AND NOT (p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP2_P1\"]')\n    \n    UNION ALL\n    \n    -- Pass status: Prospects with P2 flag in sources\n    SELECT \n        '2 - Pass' AS status,\n        2 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP2_P1\"]'\n    \n    UNION ALL\n    \n    -- Fail status: Prospects with P2 fail flag in sources\n    SELECT \n        '3 - Fail' AS status,\n        3 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:fail\"]'\n    \n    UNION ALL\n    \n    -- Inconclusive status: Prospects with P2 inconclusive flag in sources\n    SELECT \n        '4 - Inconclusive' AS status,\n        4 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:inconclusive\"]'\n      -- AND p.email_status = 'INCONCLUSIVE'\n    \n    UNION ALL\n    \n    -- Success status: Prospects with P2 success flag in sources\n    SELECT \n        '5 - Success' AS status,\n        5 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:success\"]'\n    \n    UNION ALL\n    \n    -- Missed status: Prospects in Pass but not in Fail, Inconclusive, or Success\n    SELECT \n        '6 - Missed' AS status,\n        6 AS sort_order,\n        p.company_id\n    FROM public.\"Prospect\" p\n    WHERE p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP2_P1\"]'  -- Has Pass condition\n      AND NOT (p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:fail\"]')  -- Not in Fail\n      AND NOT (p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:inconclusive\"]')  -- Not in Inconclusive\n      AND NOT (p.sources::jsonb @> '[\"EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:success\"]')  -- Not in Success\n),\n-- Count actual occurrences\nactual_counts AS (\n    SELECT \n        sc.status,\n        sc.sort_order,\n        sp.jeff_search_priority,\n        COUNT(*) AS count\n    FROM status_counts sc\n    JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n    GROUP BY \n        sc.status, \n        sc.sort_order,\n        sp.jeff_search_priority\n)\n-- Final query with NULL counts for missing combinations\nSELECT \n    ac.status,\n    ac.jeff_search_priority,\n    COALESCE(actual.count, 0) AS count  -- Use 0 instead of NULL for missing combinations\nFROM all_combinations ac\nLEFT JOIN actual_counts actual ON \n    ac.status = actual.status AND\n    ac.jeff_search_priority = actual.jeff_search_priority\nORDER BY \n    ac.sort_order,\n    ac.jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 157, "name": "Apollo Prospect Search Funnel 2nd Pass", "display": "table", "query_type": "native", "collection": "Aggregates", "tables": [{"name": "status_options", "schema": "public"}, {"name": "public", "schema": "public"}, {"name": "companies_with_apollo_p2_prospects", "schema": "public"}, {"name": "companies_with_apollo_p1_prospects", "schema": "public"}, {"name": "status_counts", "schema": "public"}, {"name": "all_combinations", "schema": "public"}, {"name": "priority_options", "schema": "public"}, {"name": "actual_counts", "schema": "public"}], "sql": "-- First, get all possible status values\nWITH status_options AS (\n    SELECT '1 - Eligible' AS status, 1 AS sort_order\n    UNION ALL SELECT '2 - Passed' AS status, 2 AS sort_order\n    UNION ALL SELECT '3 - Failed' AS status, 3 AS sort_order\n    UNION ALL SELECT '4 - Success' AS status, 4 AS sort_order\n),\n\n-- Get a distinct list of all jeff_search_priority values\npriority_options AS (\n    SELECT DISTINCT jeff_search_priority\n    FROM {{#119-sellers-with-search-priority}}\n),\n\n-- Create all possible combinations of status and priority\nall_combinations AS (\n    SELECT \n        so.status,\n        so.sort_order,\n        po.jeff_search_priority\n    FROM status_options so\n    CROSS JOIN priority_options po\n),\n\n-- Phase 1: Companies with APOLLO P1 in prospect sources\ncompanies_with_apollo_p1_prospects AS (\n    SELECT DISTINCT\n        c.id\n    FROM public.\"Company\" c\n    JOIN public.\"Prospect\" p ON c.id = p.company_id\n    WHERE p.sources::jsonb @> '[\"PROSPECT_SEARCH_APOLLO_STEP1_P1\"]'\n      AND c.website_status = 'Final Correct'\n),\n\n-- Phase 2: Companies with APOLLO P2 in prospect sources\ncompanies_with_apollo_p2_prospects AS (\n    SELECT DISTINCT\n        c.id\n    FROM public.\"Company\" c\n    JOIN public.\"Prospect\" p ON c.id = p.company_id\n    WHERE p.sources::jsonb @> '[\"PROSPECT_SEARCH_APOLLO_STEP2_P1\"]'\n      AND c.website_status = 'Final Correct'\n),\n\n-- Original query to get actual counts with MUTUALLY EXCLUSIVE categories\nstatus_counts AS (\n    -- Success status (highest priority): Final Correct companies with 1+ prospects with APOLLO P2 in sources\n    SELECT \n        '4 - Success' AS status,\n        4 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status = 'Final Correct'\n      AND c.id IN (SELECT id FROM companies_with_apollo_p2_prospects)\n    \n    UNION ALL\n    \n    -- Failed status: Final Correct companies with APOLLO P2 in lookup_sources but no prospects with APOLLO P2\n    SELECT \n        '3 - Failed' AS status,\n        3 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status = 'Final Correct'\n      AND c.lookup_sources @> '[\"PROSPECT_SEARCH_APOLLO_STEP2_P1\"]'\n      AND c.id NOT IN (SELECT id FROM companies_with_apollo_p2_prospects)\n    \n    UNION ALL\n    \n    -- Eligible status: Companies that failed in Phase 1 (have P1 flag but no P1 prospects and no P2 flag)\n    SELECT \n        '1 - Eligible' AS status,\n        1 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status = 'Final Correct'\n      AND c.lookup_sources @> '[\"PROSPECT_SEARCH_APOLLO_STEP1_P1\"]'\n      AND c.id NOT IN (SELECT id FROM companies_with_apollo_p1_prospects)\n      AND NOT (c.lookup_sources @> '[\"PROSPECT_SEARCH_APOLLO_STEP2_P1\"]')\n      AND c.id NOT IN (SELECT id FROM companies_with_apollo_p2_prospects)\n),\n\n-- Aggregate the actual counts\nactual_counts AS (\n    SELECT \n        sc.status,\n        sc.sort_order,\n        sp.jeff_search_priority,\n        COUNT(*) AS count\n    FROM status_counts sc\n    JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n    GROUP BY \n        sc.status, \n        sc.sort_order,\n        sp.jeff_search_priority\n)\n\n-- Final query with NULL counts for missing combinations\nSELECT \n    ac.status,\n    ac.jeff_search_priority,\n    COALESCE(actual.count, 0) AS count  -- Use 0 instead of NULL for missing combinations\nFROM all_combinations ac\nLEFT JOIN actual_counts actual ON \n    ac.status = actual.status AND\n    ac.jeff_search_priority = actual.jeff_search_priority\nORDER BY \n    ac.sort_order,\n    ac.jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 188, "name": "eMails Eligible to be Reached Out (30 days since last contact on SmartLead)", "display": "pivot", "query_type": "query", "collection": "Download Jeff <PERSON>s", "tables": [], "sql": null, "source_query": 186, "aggregations": [["count"]], "filters": ["and", ["in", ["field", "Prospects with eMail Classification__email_status", {"base-type": "type/Text"}], "CATCHALL", "GREYLISTING", "VERIFIED"], ["or", [">=", ["datetime-diff", ["field", "lastEmailSentTime", {"base-type": "type/DateTime"}], ["now"], "day"], 30], ["is-null", ["field", "lastEmailSentTime", {"base-type": "type/DateTime"}]]], ["!=", ["field", "jeff_search_priority", {"base-type": "type/Text"}], "SP9"]], "breakouts": [["field", "jeff_search_priority", {"base-type": "type/Text"}]]}, {"id": 190, "name": "# of Prospects Never Reached Out (as per our Internal Tracking)", "display": "scalar", "query_type": "query", "collection": "Download Jeff <PERSON>s", "tables": [], "sql": null, "source_query": 186, "aggregations": [["count"]], "filters": ["and", ["in", ["field", "Prospects with eMail Classification__email_status", {"base-type": "type/Text"}], "CATCHALL", "GREYLISTING", "VERIFIED"], ["or", [">=", ["datetime-diff", ["field", "lastEmailSentTime", {"base-type": "type/DateTime"}], ["now"], "day"], 30], ["is-null", ["field", "lastEmailSentTime", {"base-type": "type/DateTime"}]]], ["!=", ["field", "jeff_search_priority", {"base-type": "type/Text"}], "SP9"], ["is-null", ["field", "lastEmailSentTime", {"base-type": "type/DateTime"}]]], "breakouts": []}, {"id": 191, "name": "Apollo Prospect Search Funnel 3rd Pass", "display": "table", "query_type": "native", "collection": "Aggregates", "tables": [{"name": "status_options", "schema": "public"}, {"name": "public", "schema": "public"}, {"name": "companies_with_apollo_p3_prospects", "schema": "public"}, {"name": "companies_with_apollo_p2_prospects", "schema": "public"}, {"name": "status_counts", "schema": "public"}, {"name": "all_combinations", "schema": "public"}, {"name": "priority_options", "schema": "public"}, {"name": "actual_counts", "schema": "public"}], "sql": "-- First, get all possible status values\nWITH status_options AS (\n    SELECT '1 - Eligible' AS status, 1 AS sort_order\n    UNION ALL SELECT '2 - Passed' AS status, 2 AS sort_order\n    UNION ALL SELECT '3 - Failed' AS status, 3 AS sort_order\n    UNION ALL SELECT '4 - Success' AS status, 4 AS sort_order\n),\n\n-- Get a distinct list of all jeff_search_priority values\npriority_options AS (\n    SELECT DISTINCT jeff_search_priority\n    FROM {{#119-sellers-with-search-priority}}\n),\n\n-- Create all possible combinations of status and priority\nall_combinations AS (\n    SELECT \n        so.status,\n        so.sort_order,\n        po.jeff_search_priority\n    FROM status_options so\n    CROSS JOIN priority_options po\n),\n\n-- Phase 2: Companies with APOLLO P2 in prospect sources\ncompanies_with_apollo_p2_prospects AS (\n    SELECT DISTINCT\n        c.id\n    FROM public.\"Company\" c\n    JOIN public.\"Prospect\" p ON c.id = p.company_id\n    WHERE p.sources::jsonb @> '[\"PROSPECT_SEARCH_APOLLO_STEP2_P1\"]'\n      AND c.website_status = 'Final Correct'\n),\n\n-- Phase 3: Companies with APOLLO P3 in prospect sources\ncompanies_with_apollo_p3_prospects AS (\n    SELECT DISTINCT\n        c.id\n    FROM public.\"Company\" c\n    JOIN public.\"Prospect\" p ON c.id = p.company_id\n    WHERE p.sources::jsonb @> '[\"PROSPECT_SEARCH_APOLLO_STEP3_P1\"]'\n      AND c.website_status = 'Final Correct'\n),\n\n-- Original query to get actual counts with MUTUALLY EXCLUSIVE categories\nstatus_counts AS (\n    -- Success status (highest priority): Final Correct companies with 1+ prospects with APOLLO P3 in sources\n    SELECT \n        '4 - Success' AS status,\n        4 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status = 'Final Correct'\n      AND c.id IN (SELECT id FROM companies_with_apollo_p3_prospects)\n    \n    UNION ALL\n    \n    -- Failed status: Final Correct companies with APOLLO P3 in lookup_sources but no prospects with APOLLO P3\n    SELECT \n        '3 - Failed' AS status,\n        3 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status = 'Final Correct'\n      AND c.lookup_sources @> '[\"PROSPECT_SEARCH_APOLLO_STEP3_P1\"]'\n      AND c.id NOT IN (SELECT id FROM companies_with_apollo_p3_prospects)\n    \n    UNION ALL\n    \n    -- Eligible status: Companies that failed in Phase 2 (have P2 flag but no P2 prospects and no P3 flag)\n    SELECT \n        '1 - Eligible' AS status,\n        1 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.website_status = 'Final Correct'\n      AND c.lookup_sources @> '[\"PROSPECT_SEARCH_APOLLO_STEP2_P1\"]'  -- Has been attempted in Phase 2\n      AND c.id NOT IN (SELECT id FROM companies_with_apollo_p2_prospects)  -- Failed Phase 2 (no P2 prospects)\n      AND NOT (c.lookup_sources @> '[\"PROSPECT_SEARCH_APOLLO_STEP3_P1\"]')  -- Not yet attempted in Phase 3\n      AND c.id NOT IN (SELECT id FROM companies_with_apollo_p3_prospects)  -- No Phase 3 prospects yet\n),\n\n-- Aggregate the actual counts\nactual_counts AS (\n    SELECT \n        sc.status,\n        sc.sort_order,\n        sp.jeff_search_priority,\n        COUNT(*) AS count\n    FROM status_counts sc\n    JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n    GROUP BY \n        sc.status, \n        sc.sort_order,\n        sp.jeff_search_priority\n)\n\n-- Final query with NULL counts for missing combinations\nSELECT \n    ac.status,\n    ac.jeff_search_priority,\n    COALESCE(actual.count, 0) AS count  -- Use 0 instead of NULL for missing combinations\nFROM all_combinations ac\nLEFT JOIN actual_counts actual ON \n    ac.status = actual.status AND\n    ac.jeff_search_priority = actual.jeff_search_priority\nORDER BY \n    ac.sort_order,\n    ac.jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 272, "name": "Anymail Finder Prospect Search Funnel", "display": "table", "query_type": "native", "collection": "Aggregates", "tables": [{"name": "status_options", "schema": "public"}, {"name": "companies_with_anymail_prospects", "schema": "public"}, {"name": "status_counts", "schema": "public"}, {"name": "all_combinations", "schema": "public"}, {"name": "priority_options", "schema": "public"}, {"name": "public", "schema": "public"}, {"name": "actual_counts", "schema": "public"}], "sql": "-- First, get all possible status values\nWITH status_options AS (\n    SELECT '1 - Eligible' AS status, 1 AS sort_order\n    UNION ALL SELECT '2 - Passed' AS status, 2 AS sort_order\n    UNION ALL SELECT '3 - Failed' AS status, 3 AS sort_order\n    UNION ALL SELECT '4 - Success' AS status, 4 AS sort_order\n),\n\n-- Get a distinct list of all jeff_search_priority values\npriority_options AS (\n    SELECT DISTINCT jeff_search_priority\n    FROM {{#119-sellers-with-search-priority}}\n),\n\n-- Create all possible combinations of status and priority\nall_combinations AS (\n    SELECT \n        so.status,\n        so.sort_order,\n        po.jeff_search_priority\n    FROM status_options so\n    CROSS JOIN priority_options po\n),\n\n-- Companies with ANYMAIL_FINDER prospects (SUCCESS category)\ncompanies_with_anymail_prospects AS (\n    SELECT DISTINCT\n        sp.id\n    FROM {{#119-sellers-with-search-priority}} sp\n    JOIN public.\"Prospect\" p ON sp.id = p.company_id\n    WHERE p.sources::jsonb @> '[\"PROSPECT_SEARCH_ANYMAIL_FINDER_P1\"]'\n),\n\n-- Query to get actual counts with <PERSON><PERSON><PERSON><PERSON>LY EXCLUSIVE categories\nstatus_counts AS (\n    -- Success status: Companies with ANY<PERSON>IL_FINDER in lookup_sources AND have prospects with ANYMAIL_FINDER\n    SELECT \n        '4 - Success' AS status,\n        4 AS sort_order,\n        sp.id AS company_id\n    FROM {{#119-sellers-with-search-priority}} sp\n    WHERE sp.lookup_sources @> '[\"PROSPECT_SEARCH_ANYMAIL_FINDER_P1\"]'\n      AND sp.id IN (SELECT id FROM companies_with_anymail_prospects)\n    \n    UNION ALL\n    \n    -- Failed status: Companies with ANYMAIL_FINDER in lookup_sources but NO prospects with ANYMAIL_FINDER\n    SELECT \n        '3 - Failed' AS status,\n        3 AS sort_order,\n        sp.id AS company_id\n    FROM {{#119-sellers-with-search-priority}} sp\n    WHERE sp.lookup_sources @> '[\"PROSPECT_SEARCH_ANYMAIL_FINDER_P1\"]'\n      AND sp.id NOT IN (SELECT id FROM companies_with_anymail_prospects)\n    \n    UNION ALL\n    \n    -- Passed status: Companies with ANYMAIL_FINDER in lookup_sources (includes both Success and Failed)\n    SELECT \n        '2 - Passed' AS status,\n        2 AS sort_order,\n        sp.id AS company_id\n    FROM {{#119-sellers-with-search-priority}} sp\n    WHERE sp.lookup_sources @> '[\"PROSPECT_SEARCH_ANYMAIL_FINDER_P1\"]'\n    \n    UNION ALL\n    \n    -- Eligible status: Companies with all three previous steps completed and jeff_num_unverified = 0\n    SELECT \n        '1 - Eligible' AS status,\n        1 AS sort_order,\n        sp.id AS company_id\n    FROM {{#119-sellers-with-search-priority}} sp\n    WHERE sp.lookup_sources @> '[\"PROSPECT_SEARCH_APOLLO_STEP1_P1\"]'\n      AND sp.lookup_sources @> '[\"PROSPECT_SEARCH_APOLLO_STEP2_P1\"]'\n      AND sp.lookup_sources @> '[\"PROSPECT_SEARCH_DOMAIN_EMAIL_HARVEST_P1\"]'\n      AND sp.jeff_num_unverified = 0\n\t  AND sp.jeff_num_prospects = 0 \n),\n\n-- Aggregate the actual counts\nactual_counts AS (\n    SELECT \n        sc.status,\n        sc.sort_order,\n        sp.jeff_search_priority,\n        COUNT(*) AS count\n    FROM status_counts sc\n    JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n    GROUP BY \n        sc.status, \n        sc.sort_order,\n        sp.jeff_search_priority\n)\n\n-- Final query with NULL counts for missing combinations\nSELECT \n    ac.status,\n    ac.jeff_search_priority,\n    COALESCE(actual.count, 0) AS count  -- Use 0 instead of NULL for missing combinations\nFROM all_combinations ac\nLEFT JOIN actual_counts actual ON \n    ac.status = actual.status AND\n    ac.jeff_search_priority = actual.jeff_search_priority\nORDER BY \n    ac.sort_order,\n    ac.jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 282, "name": "Number of Unique Prospects & Sellers (with eMails)", "display": "bar", "query_type": "query", "collection": "Overall Goals", "tables": [], "sql": null, "source_query": 117, "aggregations": [["distinct", ["field", "email", {"base-type": "type/Text"}]], ["distinct", ["field", "amazon_seller_id", {"base-type": "type/Text"}]]], "filters": ["in", ["field", "email_status", {"base-type": "type/Text"}], "CATCHALL", "GREYLISTING", "VERIFIED"], "breakouts": [["field", "jeff_search_priority", {"base-type": "type/Text"}]]}, {"id": 291, "name": "Smartlead Aggregate Analytics (by funnel & client)", "display": "table", "query_type": "query", "collection": "SmartLead Analytics", "tables": [], "sql": null, "source_query": 278, "aggregations": [["sum", ["field", "email_1_sent_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "replies_after_email_1_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "automated_replies_after_email_1_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "error_replies_after_email_1_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_slots_sent_after_1", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_sent_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "automated_replies_after_email_2_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "error_replies_after_email_2_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_slots_sent_after_2", {"base-type": "type/BigInteger"}]], ["sum", ["field", "replies_after_email_3_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "automated_replies_after_email_3_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "error_replies_after_email_3_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "replies_after_email_2_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_sent_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_slots_sent_after_3", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_slots_sent_unknown", {"base-type": "type/BigInteger"}]], ["sum", ["field", "total_meeting_slots_sent", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meetings_booked", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_sent_personal_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_sent_role_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_sent_work_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_sent_unknown_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_sent_personal_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_sent_role_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_sent_work_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_sent_unknown_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_sent_personal_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_sent_role_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_sent_work_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_sent_unknown_count", {"base-type": "type/BigInteger"}]]], "filters": [">", ["field", "week_start_date", {"base-type": "type/Date"}], "2025-05-01"], "breakouts": [["field", "week_start_date", {"base-type": "type/Date", "temporal-unit": "day", "original-temporal-unit": "month"}], ["field", "campaign_code", {"base-type": "type/Text"}], ["field", "jeff_client_name", {"base-type": "type/Text"}]]}, {"id": 290, "name": "Smartlead Aggregate Analytics (by Tag)", "display": "table", "query_type": "query", "collection": "SmartLead Analytics", "tables": [], "sql": null, "source_query": 289, "aggregations": [["sum", ["field", "email_1_sent_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "replies_after_email_1_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "automated_replies_after_email_1_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "error_replies_after_email_1_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_slots_sent_after_1", {"base-type": "type/BigInteger"}]], ["sum", ["field", "error_replies_after_email_2_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_slots_sent_after_2", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_sent_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "replies_after_email_2_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "automated_replies_after_email_2_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meetings_booked", {"base-type": "type/BigInteger"}]], ["sum", ["field", "error_replies_after_email_3_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_sent_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "replies_after_email_3_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "automated_replies_after_email_3_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "total_meeting_slots_sent", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_slots_sent_unknown", {"base-type": "type/BigInteger"}]]], "filters": [">", ["field", "week_start_date", {"base-type": "type/Date"}], "2025-05-01"], "breakouts": [["field", "week_start_date", {"base-type": "type/Date", "temporal-unit": "day"}], ["field", "jeff_client_name", {"base-type": "type/Text"}], ["field", "tag_name", {"base-type": "type/Text"}], ["field", "campaign_code", {"base-type": "type/Text"}], ["field", "provider_name", {"base-type": "type/Text"}]]}, {"id": 292, "name": "Reply Rate by Tag<>Provider<>Campaign", "display": "line", "query_type": "query", "collection": "Overall Analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [">", ["field", "sum", {"base-type": "type/Integer"}], 100], "breakouts": []}, {"id": 294, "name": "Prospects Reached (by Week)", "display": "bar", "query_type": "query", "collection": "Overall Analytics", "tables": [{"id": 99, "name": "FunnelClientAnalytics"}], "sql": null, "source_query": null, "aggregations": [["sum", ["field", 2403, {"base-type": "type/Integer"}]]], "filters": ["!=", ["field", 2404, {"base-type": "type/Text"}], "HKP"], "breakouts": [["field", 2400, {"base-type": "type/Date", "temporal-unit": "week"}], ["field", 2404, {"base-type": "type/Text"}]]}, {"id": 297, "name": "Raw Numbers by Funnel", "display": "table", "query_type": "native", "collection": "Overall Analytics", "tables": [{"name": "FunnelClientAnalytics", "schema": "public2"}, {"name": "public2", "schema": "public"}], "sql": "-- ============================================================================\n-- Metabase-Optimized Funnel Analytics Query for Pivot Tables\n-- ============================================================================\n-- Simplified version with key metrics for easy pivot table creation\n-- All metrics aggregated by week_start_date, campaign_code, client_name\n-- ============================================================================\n\nSELECT\n  -- Row dimension: Week\n  CAST(\n    (\n      CAST(\n        DATE_TRUNC('week', \n          CAST((\"public2\".\"FunnelClientAnalytics\".\"week_start_date\" + INTERVAL '1 day') AS date)\n        ) AS date\n      ) + INTERVAL '-1 day'\n    ) AS date\n  ) AS week_start_date,\n  \n  -- Column dimensions: Campaign and Client\n  \"public2\".\"FunnelClientAnalytics\".\"campaign_code\" AS campaign_code,\n  \"public2\".\"FunnelClientAnalytics\".\"client_name\" AS client_name,\n  \n  -- ========================================================================\n  -- EMAIL 1 METRICS & RATES\n  -- ========================================================================\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"email_1_sent\") AS email_1_sent,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"total_replies_email_1\") AS email_1_total_replies,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"replies_after_email_1\") AS email_1_replies,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"auto_replies_after_email_1\") AS email_1_auto_replies,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"error_replies_after_email_1\") AS email_1_error_replies,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"meeting_slots_after_email_1\") AS email_1_meeting_slots,\n  \n  -- Email 1 Percentage Rates\n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"email_1_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"total_replies_email_1\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"email_1_sent\"), 2)\n    ELSE 0 \n  END AS email_1_total_reply_rate,\n  \n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"email_1_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"replies_after_email_1\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"email_1_sent\"), 2)\n    ELSE 0 \n  END AS email_1_reply_rate,\n  \n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"email_1_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"auto_replies_after_email_1\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"email_1_sent\"), 2)\n    ELSE 0 \n  END AS email_1_auto_reply_rate,\n  \n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"email_1_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"error_replies_after_email_1\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"email_1_sent\"), 2)\n    ELSE 0 \n  END AS email_1_error_rate,\n  \n  -- ========================================================================\n  -- EMAIL 2 METRICS & RATES\n  -- ========================================================================\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"email_2_sent\") AS email_2_sent,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"total_replies_email_2\") AS email_2_total_replies,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"replies_after_email_2\") AS email_2_replies,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"auto_replies_after_email_2\") AS email_2_auto_replies,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"error_replies_after_email_2\") AS email_2_error_replies,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"meeting_slots_after_email_2\") AS email_2_meeting_slots,\n  \n  -- Email 2 Percentage Rates  \n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"email_2_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"total_replies_email_2\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"email_2_sent\"), 2)\n    ELSE 0 \n  END AS email_2_total_reply_rate,\n  \n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"email_2_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"replies_after_email_2\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"email_2_sent\"), 2)\n    ELSE 0 \n  END AS email_2_reply_rate,\n  \n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"email_2_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"auto_replies_after_email_2\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"email_2_sent\"), 2)\n    ELSE 0 \n  END AS email_2_auto_reply_rate,\n  \n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"email_2_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"error_replies_after_email_2\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"email_2_sent\"), 2)\n    ELSE 0 \n  END AS email_2_error_rate,\n  \n  -- ========================================================================\n  -- EMAIL 3 METRICS & RATES\n  -- ========================================================================\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"email_3_sent\") AS email_3_sent,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"total_replies_email_3\") AS email_3_total_replies,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"replies_after_email_3\") AS email_3_replies,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"auto_replies_after_email_3\") AS email_3_auto_replies,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"error_replies_after_email_3\") AS email_3_error_replies,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"meeting_slots_after_email_3\") AS email_3_meeting_slots,\n  \n  -- Email 3 Percentage Rates\n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"email_3_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"total_replies_email_3\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"email_3_sent\"), 2)\n    ELSE 0 \n  END AS email_3_total_reply_rate,\n  \n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"email_3_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"replies_after_email_3\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"email_3_sent\"), 2)\n    ELSE 0 \n  END AS email_3_reply_rate,\n  \n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"email_3_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"auto_replies_after_email_3\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"email_3_sent\"), 2)\n    ELSE 0 \n  END AS email_3_auto_reply_rate,\n  \n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"email_3_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"error_replies_after_email_3\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"email_3_sent\"), 2)\n    ELSE 0 \n  END AS email_3_error_rate,\n  \n  -- ========================================================================\n  -- MEETING METRICS\n  -- ========================================================================\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"meeting_slots_unknown\") AS meeting_slots_unknown,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"total_meeting_slots_sent\") AS total_meeting_slots_sent,\n  SUM(\"public2\".\"FunnelClientAnalytics\".\"meetings_booked\") AS meetings_booked,\n  \n  -- Meeting Conversion Rate\n  CASE \n    WHEN SUM(\"public2\".\"FunnelClientAnalytics\".\"total_meeting_slots_sent\") > 0 \n    THEN ROUND((SUM(\"public2\".\"FunnelClientAnalytics\".\"meetings_booked\") * 100.0) / SUM(\"public2\".\"FunnelClientAnalytics\".\"total_meeting_slots_sent\"), 2)\n    ELSE 0 \n  END AS meeting_booking_rate\n\nFROM \"public2\".\"FunnelClientAnalytics\"\n\nGROUP BY\n  CAST(\n    (\n      CAST(\n        DATE_TRUNC('week', \n          CAST((\"public2\".\"FunnelClientAnalytics\".\"week_start_date\" + INTERVAL '1 day') AS date)\n        ) AS date\n      ) + INTERVAL '-1 day'\n    ) AS date\n  ),\n  \"public2\".\"FunnelClientAnalytics\".\"campaign_code\",\n  \"public2\".\"FunnelClientAnalytics\".\"client_name\"\n\nORDER BY\n  week_start_date ASC,\n  \"public2\".\"FunnelClientAnalytics\".\"campaign_code\" ASC,\n  \"public2\".\"FunnelClientAnalytics\".\"client_name\" ASC;\n\n-- ============================================================================\n-- METABASE PIVOT TABLE RECOMMENDATIONS:\n-- ============================================================================\n-- OPTION 1 - Reply Rate Analysis:\n--   Rows: week_start_date\n--   Columns: campaign_code, client_name\n--   Values: email_1_reply_rate, email_2_reply_rate, email_3_reply_rate\n--\n-- OPTION 2 - Volume vs Performance:\n--   Rows: client_name  \n--   Columns: campaign_code\n--   Values: email_1_sent, email_1_reply_rate, meetings_booked\n--\n-- OPTION 3 - Error Rate Focus:\n--   Rows: week_start_date\n--   Columns: client_name\n--   Values: email_1_error_rate, email_2_error_rate, email_3_error_rate\n-- ============================================================================", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 298, "name": "Tabular View Tag Level Data", "display": "table", "query_type": "query", "collection": "Overall Analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 308, "name": "Meetings CRM Stats by Client", "display": "pivot", "query_type": "query", "collection": "Overall Analytics", "tables": [{"id": 94, "name": "MeetingsFromReplit"}], "sql": null, "source_query": null, "aggregations": [["sum", ["field", 2308, {"base-type": "type/Integer"}]], ["sum", ["field", 2306, {"base-type": "type/Integer"}]], ["sum", ["field", 2309, {"base-type": "type/Integer"}]], ["sum", ["field", 2310, {"base-type": "type/Integer"}]], ["sum", ["field", 2305, {"base-type": "type/Integer"}]], ["sum", ["expression", "Total Slots", {"base-type": "type/Integer"}]]], "filters": [">", ["field", 2307, {"base-type": "type/Date"}], "2025-05-01"], "breakouts": [["field", 2307, {"base-type": "type/Date", "temporal-unit": "week"}], ["field", "businessName", {"base-type": "type/Text", "join-alias": "Clients with Business Name - Client"}]]}, {"id": 310, "name": "Prospects to Download per Campaign Series (older than 56 days)", "display": "table", "query_type": "query", "collection": "Download Jeff <PERSON>s", "tables": [], "sql": null, "source_query": 306, "aggregations": [["count"], ["distinct", ["field", "Prospects with eMail Classification__email", {"base-type": "type/Text"}]], ["distinct", ["field", "amazon_seller_id", {"base-type": "type/Text"}]]], "filters": ["and", ["or", [">=", ["datetime-diff", ["field", "lastSentTimeAt", {"base-type": "type/DateTime"}], ["now"], "day"], 56], ["is-null", ["field", "lastSentTimeAt", {"base-type": "type/DateTime"}]]], ["!=", ["field", "jeff_search_priority", {"base-type": "type/Text"}], "SP9"], ["in", ["field", 460, {"base-type": "type/Text", "join-alias": "SellerCountryMatching - amazon_seller_id"}], "UK", "US"], ["in", ["field", "Prospects with eMail Classification__email_status", {"base-type": "type/Text"}], "CATCHALL", "VERIFIED", "GREYLISTING"]], "breakouts": [["field", "campaignCode", {"base-type": "type/Text"}]]}, {"id": 311, "name": "Pivot Table by Numbers", "display": "pivot", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": 297, "aggregations": [["sum", ["field", "email_1_sent", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_total_replies", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_error_replies", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_meeting_slots", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_total_reply_rate", {"base-type": "type/Decimal"}]], ["sum", ["field", "email_2_sent", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_total_replies", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_error_replies", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_meeting_slots", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_total_reply_rate", {"base-type": "type/Decimal"}]], ["sum", ["field", "email_3_sent", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_total_replies", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_error_replies", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_meeting_slots", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_total_reply_rate", {"base-type": "type/Decimal"}]], ["sum", ["field", "meetings_booked", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_booking_rate", {"base-type": "type/Decimal"}]]], "filters": [], "breakouts": [["field", "week_start_date", {"base-type": "type/Date", "temporal-unit": "week", "original-temporal-unit": "month"}], ["field", "campaign_code", {"base-type": "type/Text"}], ["field", "client_name", {"base-type": "type/Text"}]]}, {"id": 312, "name": "Overall Pivot Table eMails Sent", "display": "pivot", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": 297, "aggregations": [["sum", ["field", "email_1_sent", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_total_replies", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_error_replies", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_meeting_slots", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_total_reply_rate", {"base-type": "type/Decimal"}]], ["sum", ["field", "email_2_sent", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_total_replies", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_error_replies", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_meeting_slots", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_total_reply_rate", {"base-type": "type/Decimal"}]], ["sum", ["field", "email_3_sent", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_total_replies", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_error_replies", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_meeting_slots", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_total_reply_rate", {"base-type": "type/Decimal"}]], ["sum", ["field", "meetings_booked", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_booking_rate", {"base-type": "type/Decimal"}]]], "filters": [], "breakouts": [["field", "week_start_date", {"base-type": "type/Date", "temporal-unit": "week", "original-temporal-unit": "month"}], ["field", "campaign_code", {"base-type": "type/Text"}]]}, {"id": 314, "name": "Smartlead Aggregate Analytics (by provider & client)", "display": "table", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": 313, "aggregations": [["sum", ["field", "email_1_sent_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "replies_after_email_1_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "automated_replies_after_email_1_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "error_replies_after_email_1_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_sent_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "replies_after_email_2_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "automated_replies_after_email_2_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "error_replies_after_email_2_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_sent_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "replies_after_email_3_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "automated_replies_after_email_3_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "error_replies_after_email_3_count", {"base-type": "type/BigInteger"}]]], "filters": [">", ["field", "week_start_date", {"base-type": "type/Date"}], "2025-05-01"], "breakouts": [["field", "week_start_date", {"base-type": "type/Date", "temporal-unit": "week"}], ["field", "provider_name", {"base-type": "type/Text"}], ["field", "jeff_client_name", {"base-type": "type/Text"}], ["field", "campaign_code", {"base-type": "type/Text"}]]}, {"id": 315, "name": "Reply Rate by Provider<>Client<>Campaign Series", "display": "line", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [">", ["field", "sum", {"base-type": "type/Integer"}], 100], "breakouts": []}, {"id": 318, "name": "Reply Rate by Provider<>Client", "display": "line", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [">", ["field", "sum", {"base-type": "type/Integer"}], 100], "breakouts": []}, {"id": 319, "name": "Reply Rate by Client", "display": "line", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 320, "name": "Slots <PERSON> by Client", "display": "line", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [">", ["field", "date", {"base-type": "type/Date", "inherited-temporal-unit": "week"}], "2025-05-01"], "breakouts": []}, {"id": 321, "name": "Reply Rate by Campaign Series", "display": "bar", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 322, "name": "Reply Rate <PERSON> Overall", "display": "line", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 323, "name": "Slots <PERSON><PERSON>", "display": "line", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [">", ["field", "date", {"base-type": "type/Date", "inherited-temporal-unit": "week"}], "2025-05-01"], "breakouts": []}, {"id": 324, "name": "eMail 1, eMail 2, eMail 3 sent week on week", "display": "bar", "query_type": "query", "collection": "Our analytics", "tables": [{"id": 99, "name": "FunnelClientAnalytics"}], "sql": null, "source_query": null, "aggregations": [["sum", ["field", 2403, {"base-type": "type/Integer"}]], ["sum", ["field", 2410, {"base-type": "type/Integer"}]], ["sum", ["field", 2418, {"base-type": "type/Integer"}]]], "filters": [], "breakouts": [["field", 2400, {"base-type": "type/Date", "temporal-unit": "week"}]]}, {"id": 325, "name": "eMail 1 Sent by Role Week on Week", "display": "line", "query_type": "query", "collection": "Our analytics", "tables": [{"id": 99, "name": "FunnelClientAnalytics"}], "sql": null, "source_query": null, "aggregations": [["sum", ["field", 2492, {"base-type": "type/Integer"}]], ["sum", ["field", 2494, {"base-type": "type/Integer"}]], ["sum", ["field", 2490, {"base-type": "type/Integer"}]], ["sum", ["field", 2491, {"base-type": "type/Integer"}]]], "filters": [], "breakouts": [["field", 2400, {"base-type": "type/Date", "temporal-unit": "week"}]]}, {"id": 326, "name": "Prospects Reached by Client", "display": "area", "query_type": "query", "collection": "Overall Analytics", "tables": [{"id": 99, "name": "FunnelClientAnalytics"}], "sql": null, "source_query": null, "aggregations": [["sum", ["field", 2403, {"base-type": "type/Integer"}]]], "filters": [], "breakouts": [["field", 2400, {"base-type": "type/Date", "temporal-unit": "week"}], ["field", 2423, {"base-type": "type/Text"}]]}, {"id": 327, "name": "Overall (till date) client wise Reply Rate", "display": "line", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 328, "name": "Reply Rate by Tag<>Provider", "display": "line", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [">", ["field", "sum", {"base-type": "type/Integer"}], 100], "breakouts": []}, {"id": 329, "name": "Error Rate by Provider<>Client", "display": "line", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [">", ["field", "sum", {"base-type": "type/Integer"}], 100], "breakouts": []}, {"id": 331, "name": "Error Rate by Client", "display": "line", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 333, "name": "Error Rate by Tag<>Provider", "display": "line", "query_type": "query", "collection": "Our analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [">", ["field", "sum", {"base-type": "type/Integer"}], 100], "breakouts": []}, {"id": 334, "name": "Error Rate by Tag<>Provider<>Campaign", "display": "line", "query_type": "query", "collection": "Overall Analytics", "tables": [], "sql": null, "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 335, "name": "Slots Sent by Funnel", "display": "bar", "query_type": "query", "collection": "Our analytics", "tables": [{"id": 94, "name": "MeetingsFromReplit"}], "sql": null, "source_query": null, "aggregations": [["sum", ["expression", "Total Slots", {"base-type": "type/Integer"}]]], "filters": [], "breakouts": [["field", 2307, {"base-type": "type/Date", "temporal-unit": "week"}], ["field", 2313, {"base-type": "type/Text"}]]}, {"id": 337, "name": "Meetings Booked by Funnel", "display": "bar", "query_type": "query", "collection": "Our analytics", "tables": [{"id": 94, "name": "MeetingsFromReplit"}], "sql": null, "source_query": null, "aggregations": [["sum", ["field", 2305, {"base-type": "type/Integer"}]]], "filters": [], "breakouts": [["field", 2307, {"base-type": "type/Date", "temporal-unit": "week"}], ["field", 2313, {"base-type": "type/Text"}]]}, {"id": 365, "name": "# Smartlead of Errors in Last 60 Days", "display": "scalar", "query_type": "query", "collection": "Download Jeff <PERSON>s", "tables": [], "sql": null, "source_query": 364, "aggregations": [["count"]], "filters": [], "breakouts": []}, {"id": 367, "name": "Equal SERP New Query (`shop` suffix) Funnel Analysis", "display": "table", "query_type": "native", "collection": "Aggregates", "tables": [{"name": "public", "schema": "public"}, {"name": "status_options", "schema": "public"}, {"name": "status_counts", "schema": "public"}, {"name": "all_combinations", "schema": "public"}, {"name": "priority_options", "schema": "public"}, {"name": "actual_counts", "schema": "public"}], "sql": "-- First, get all possible status values\nWITH status_options AS (\n    SELECT '1 - Eligible' AS status, 1 AS sort_order\n    UNION ALL SELECT '2 - Pass' AS status, 2 AS sort_order\n    UNION ALL SELECT '3 - Fail' AS status, 3 AS sort_order\n    UNION ALL SELECT '4 - Success' AS status, 4 AS sort_order\n    UNION ALL SELECT '5 - Missed' AS status, 5 AS sort_order\n),\n\n-- Get the actual data counts\nstatus_counts AS (\n    -- Eligible status - Companies that failed the previous funnel\n    SELECT \n        '1 - Eligible' AS status,\n        1 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P4:fail\"]'\n\tAND NOT (c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P5\"]')\n\tAND c.website_status != 'Final Correct'\n    \n    UNION ALL\n    \n    -- Pass status - Companies that entered the new funnel\n    SELECT \n        '2 - Pass' AS status,\n        2 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P5\"]'\n    \n    UNION ALL\n    \n    -- Fail status - Companies that failed the new funnel\n    SELECT \n        '3 - Fail' AS status,\n        3 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P5:fail\"]'\n    \n    UNION ALL\n    \n    -- Success status - Companies that succeeded in the new funnel\n    SELECT \n        '4 - Success' AS status,\n        4 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P5:success\"]'\n    \n    UNION ALL\n    \n    -- Missed status - Companies in Pass but not in Fail or Success\n    SELECT \n        '5 - Missed' AS status,\n        5 AS sort_order,\n        c.id AS company_id\n    FROM public.\"Company\" c\n    WHERE c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P5\"]'  -- Has Pass condition\n      AND NOT (c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P5:fail\"]')  -- Not in Fail\n      AND NOT (c.lookup_sources @> '[\"WEBSITE_SEARCH_FULL_SERP_P5:success\"]')  -- Not in Success\n),\n\n-- Get a distinct list of all jeff_search_priority values\npriority_options AS (\n    SELECT DISTINCT jeff_search_priority\n    FROM {{#119-sellers-with-search-priority}}\n),\n\n-- Create all possible combinations of status and priority\nall_combinations AS (\n    SELECT \n        so.status,\n        so.sort_order,\n        po.jeff_search_priority\n    FROM status_options so\n    CROSS JOIN priority_options po\n),\n\n-- Count actual occurrences\nactual_counts AS (\n    SELECT \n        sc.status,\n        sc.sort_order,\n        sp.jeff_search_priority,\n        COUNT(*) AS count\n    FROM status_counts sc\n    JOIN {{#119-sellers-with-search-priority}} sp ON sc.company_id = sp.id\n    GROUP BY \n        sc.status, \n        sc.sort_order,\n        sp.jeff_search_priority\n)\n\n-- Final query with zeros for missing combinations\nSELECT \n    ac.status,\n    ac.jeff_search_priority,\n    ac.sort_order,\n    COALESCE(actual.count, 0) AS count  -- Replace NULL with 0 for missing combinations\nFROM all_combinations ac\nLEFT JOIN actual_counts actual ON \n    ac.status = actual.status AND\n    ac.jeff_search_priority = actual.jeff_search_priority\nORDER BY \n    ac.sort_order,\n    ac.jeff_search_priority;", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 117, "name": "Sellers with SP Classifications & Prospects (w eMails)", "display": "table", "query_type": "query", "collection": "Internal Queries", "tables": [], "sql": null, "source_query": 119, "aggregations": [], "filters": ["and", ["not-null", ["field", "prospect_id", {"base-type": "type/Integer", "join-alias": "Prospects with eMail Classification"}]], ["not-empty", ["field", "email", {"base-type": "type/Text", "join-alias": "Prospects with eMail Classification"}]]], "breakouts": []}, {"id": 119, "name": "Sellers with Search Priority", "display": "table", "query_type": "native", "collection": "Internal Queries", "tables": [{"name": "public", "schema": "public"}], "sql": "select * from \"public\".\"SellersWithSearchPriority\";", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 186, "name": "eMails with Last Sent Date (from Smartlead)", "display": "table", "query_type": "native", "collection": "Internal Queries", "tables": [{"name": "Email", "schema": "public"}, {"name": "email_last_sent", "schema": "public"}], "sql": "WITH email_last_sent AS (\n  SELECT \n    \"toEmailID\",\n    MAX(time) AS \"lastEmailSentTime\"\n  FROM \n    \"Email\"\n  GROUP BY \n    \"toEmailID\"\n)\n\nSELECT\n  base.*,\n  els.\"lastEmailSentTime\"\nFROM\n  {{#271-seller-group-sellers-with-sp-classifications-prospects-w-emails}} base\n  LEFT JOIN email_last_sent els ON base.\"Seller Group - Prospects with eMail Classification__email\" = els.\"toEmailID\"", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 278, "name": "Smartlead Analytics", "display": "table", "query_type": "native", "collection": "Internal Queries", "tables": [{"name": "DATE", "schema": "public"}, {"name": "email_analytics_base", "schema": "public"}, {"name": "MeetingsFromReplit", "schema": "public2"}, {"name": "lead_sequences", "schema": "public"}, {"name": "public2", "schema": "public"}], "sql": "WITH email_analytics_base AS (\n  SELECT \n    DATE(emails.\"time\") AS email_date,\n    DATE(DATE(emails.\"time\") - EXTRACT(DOW FROM DATE(emails.\"time\")) * INTERVAL '1 day') AS week_start_date,\n    emails.\"jeff_client_name\",\n    emails.\"jeff_campaign_code\",\n    emails.\"clientSmartLeadId\",\n    emails.\"leadId\",\n    emails.\"email_seq_number\",\n    emails.\"type\",\n    emails.\"jeff_email_status\",\n    emails.\"threadStartEmailId\",\n    emails.\"prospect_email_type\",\n    \n    -- Create flags for email sequences sent (avoids multiple scans)\n    CASE WHEN emails.\"email_seq_number\" = '1' AND emails.\"type\" = 'SENT' THEN 1 ELSE 0 END AS email_1_sent,\n    CASE WHEN emails.\"email_seq_number\" = '2' AND emails.\"type\" = 'SENT' THEN 1 ELSE 0 END AS email_2_sent,\n    CASE WHEN emails.\"email_seq_number\" = '3' AND emails.\"type\" = 'SENT' THEN 1 ELSE 0 END AS email_3_sent,\n    \n    -- Create flags for email sequences sent by prospect type\n    -- Email 1\n    CASE WHEN emails.\"email_seq_number\" = '1' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'personal' THEN 1 ELSE 0 END AS email_1_sent_personal,\n    CASE WHEN emails.\"email_seq_number\" = '1' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'role' THEN 1 ELSE 0 END AS email_1_sent_role,\n    CASE WHEN emails.\"email_seq_number\" = '1' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'work' THEN 1 ELSE 0 END AS email_1_sent_work,\n    CASE WHEN emails.\"email_seq_number\" = '1' AND emails.\"type\" = 'SENT' AND (emails.\"prospect_email_type\" IS NULL OR emails.\"prospect_email_type\" = '') THEN 1 ELSE 0 END AS email_1_sent_unknown,\n    \n    -- Email 2\n    CASE WHEN emails.\"email_seq_number\" = '2' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'personal' THEN 1 ELSE 0 END AS email_2_sent_personal,\n    CASE WHEN emails.\"email_seq_number\" = '2' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'role' THEN 1 ELSE 0 END AS email_2_sent_role,\n    CASE WHEN emails.\"email_seq_number\" = '2' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'work' THEN 1 ELSE 0 END AS email_2_sent_work,\n    CASE WHEN emails.\"email_seq_number\" = '2' AND emails.\"type\" = 'SENT' AND (emails.\"prospect_email_type\" IS NULL OR emails.\"prospect_email_type\" = '') THEN 1 ELSE 0 END AS email_2_sent_unknown,\n    \n    -- Email 3\n    CASE WHEN emails.\"email_seq_number\" = '3' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'personal' THEN 1 ELSE 0 END AS email_3_sent_personal,\n    CASE WHEN emails.\"email_seq_number\" = '3' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'role' THEN 1 ELSE 0 END AS email_3_sent_role,\n    CASE WHEN emails.\"email_seq_number\" = '3' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'work' THEN 1 ELSE 0 END AS email_3_sent_work,\n    CASE WHEN emails.\"email_seq_number\" = '3' AND emails.\"type\" = 'SENT' AND (emails.\"prospect_email_type\" IS NULL OR emails.\"prospect_email_type\" = '') THEN 1 ELSE 0 END AS email_3_sent_unknown,\n    \n    -- Create flags for replies\n    CASE WHEN emails.\"jeff_email_status\" = 'REPLY' THEN 1 ELSE 0 END AS is_reply,\n    CASE WHEN emails.\"jeff_email_status\" = 'REPLY_CS_AUTOMATED' THEN 1 ELSE 0 END AS is_auto_reply,\n    CASE WHEN emails.\"jeff_email_status\" = 'ERROR_REPLY' THEN 1 ELSE 0 END AS is_error_reply\n    \n  FROM {{#277-smartlead-emails-with-custom-reply-status}} AS emails\n),\nlead_sequences AS (\n  SELECT \n    \"leadId\",\n    MAX(email_1_sent) AS has_email_1_sent,\n    MAX(email_2_sent) AS has_email_2_sent,\n    MAX(email_3_sent) AS has_email_3_sent\n  FROM email_analytics_base\n  GROUP BY \"leadId\"\n)\n\nSELECT \n  eab.email_date,\n  eab.week_start_date,\n  eab.\"jeff_client_name\",\n  eab.\"jeff_campaign_code\" AS campaign_code,\n  eab.\"clientSmartLeadId\" AS client_id,\n  \n  -- Email counts (total)\n  SUM(eab.email_1_sent) AS email_1_sent_count,\n  SUM(eab.email_2_sent) AS email_2_sent_count,\n  SUM(eab.email_3_sent) AS email_3_sent_count,\n  \n  -- Email 1 counts by prospect type\n  SUM(eab.email_1_sent_personal) AS email_1_sent_personal_count,\n  SUM(eab.email_1_sent_role) AS email_1_sent_role_count,\n  SUM(eab.email_1_sent_work) AS email_1_sent_work_count,\n  SUM(eab.email_1_sent_unknown) AS email_1_sent_unknown_count,\n  \n  -- Email 2 counts by prospect type\n  SUM(eab.email_2_sent_personal) AS email_2_sent_personal_count,\n  SUM(eab.email_2_sent_role) AS email_2_sent_role_count,\n  SUM(eab.email_2_sent_work) AS email_2_sent_work_count,\n  SUM(eab.email_2_sent_unknown) AS email_2_sent_unknown_count,\n  \n  -- Email 3 counts by prospect type\n  SUM(eab.email_3_sent_personal) AS email_3_sent_personal_count,\n  SUM(eab.email_3_sent_role) AS email_3_sent_role_count,\n  SUM(eab.email_3_sent_work) AS email_3_sent_work_count,\n  SUM(eab.email_3_sent_unknown) AS email_3_sent_unknown_count,\n  \n  -- Reply counts using pre-calculated flags\n  SUM(CASE WHEN eab.is_reply = 1 AND ls.has_email_1_sent = 1 THEN 1 ELSE 0 END) AS replies_after_email_1_count,\n  SUM(CASE WHEN eab.is_reply = 1 AND ls.has_email_2_sent = 1 THEN 1 ELSE 0 END) AS replies_after_email_2_count,\n  SUM(CASE WHEN eab.is_reply = 1 AND ls.has_email_3_sent = 1 THEN 1 ELSE 0 END) AS replies_after_email_3_count,\n  \n  -- Auto reply counts\n  SUM(CASE WHEN eab.is_auto_reply = 1 AND ls.has_email_1_sent = 1 THEN 1 ELSE 0 END) AS automated_replies_after_email_1_count,\n  SUM(CASE WHEN eab.is_auto_reply = 1 AND ls.has_email_2_sent = 1 THEN 1 ELSE 0 END) AS automated_replies_after_email_2_count,\n  SUM(CASE WHEN eab.is_auto_reply = 1 AND ls.has_email_3_sent = 1 THEN 1 ELSE 0 END) AS automated_replies_after_email_3_count,\n  \n  -- Error reply counts  \n  SUM(CASE WHEN eab.is_error_reply = 1 AND ls.has_email_1_sent = 1 THEN 1 ELSE 0 END) AS error_replies_after_email_1_count,\n  SUM(CASE WHEN eab.is_error_reply = 1 AND ls.has_email_2_sent = 1 THEN 1 ELSE 0 END) AS error_replies_after_email_2_count,\n  SUM(CASE WHEN eab.is_error_reply = 1 AND ls.has_email_3_sent = 1 THEN 1 ELSE 0 END) AS error_replies_after_email_3_count,\n  \n  -- Meeting analytics (simplified)\n  SUM(DISTINCT COALESCE(meetings.slots_sent_after_1, 0)) AS meeting_slots_sent_after_1,\n  SUM(DISTINCT COALESCE(meetings.slots_sent_after_2, 0)) AS meeting_slots_sent_after_2,\n  SUM(DISTINCT COALESCE(meetings.slots_sent_after_3, 0)) AS meeting_slots_sent_after_3,\n  SUM(DISTINCT COALESCE(meetings.slots_sent_unknown, 0)) AS meeting_slots_sent_unknown,\n  (SUM(DISTINCT COALESCE(meetings.slots_sent_after_1, 0)) + \n   SUM(DISTINCT COALESCE(meetings.slots_sent_after_2, 0)) + \n   SUM(DISTINCT COALESCE(meetings.slots_sent_after_3, 0)) + \n   SUM(DISTINCT COALESCE(meetings.slots_sent_unknown, 0))) AS total_meeting_slots_sent,\n  SUM(DISTINCT COALESCE(meetings.booked, 0)) AS meetings_booked\n\nFROM email_analytics_base eab\nLEFT JOIN lead_sequences ls ON eab.\"leadId\" = ls.\"leadId\"\nLEFT JOIN \"public2\".\"MeetingsFromReplit\" AS meetings \n  ON eab.\"clientSmartLeadId\" = meetings.\"client\" \n  AND eab.email_date = meetings.\"date\"\n  AND eab.\"jeff_campaign_code\" = meetings.\"funnel\"\n  AND eab.\"threadStartEmailId\" = meetings.\"fromEmailId\"\n\nGROUP BY \n  eab.email_date,\n  eab.week_start_date,\n  eab.\"jeff_client_name\",\n  eab.\"jeff_campaign_code\",\n  eab.\"clientSmartLeadId\"\n\nORDER BY \n  eab.email_date DESC,\n  eab.\"jeff_client_name\",\n  eab.\"jeff_campaign_code\",\n  eab.\"clientSmartLeadId\";", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 289, "name": "Smartlead Analytics (with Tags)", "display": "table", "query_type": "native", "collection": "Internal Queries", "tables": [{"name": "MeetingsFromReplit", "schema": "public2"}, {"name": "public2", "schema": "public"}, {"name": "DATE", "schema": "public"}, {"name": "InboxesFromReplit", "schema": "public2"}, {"name": "InboxTagsFromReplit", "schema": "public2"}, {"name": "the", "schema": "public"}, {"name": "email_sent_flags", "schema": "public"}, {"name": "to", "schema": "public"}, {"name": "meeting_agg", "schema": "public"}], "sql": "-- Optimized version - eliminates repeated EXISTS subqueries\nWITH email_sent_flags AS (\n  -- Pre-calculate which leads have sent each email sequence\n  SELECT \n    \"leadId\",\n    MAX(CASE WHEN \"email_seq_number\" = '1' AND \"type\" = 'SENT' THEN 1 ELSE 0 END) AS has_sent_email_1,\n    MAX(CASE WHEN \"email_seq_number\" = '2' AND \"type\" = 'SENT' THEN 1 ELSE 0 END) AS has_sent_email_2,\n    MAX(CASE WHEN \"email_seq_number\" = '3' AND \"type\" = 'SENT' THEN 1 ELSE 0 END) AS has_sent_email_3\n  FROM {{#277-smartlead-emails-with-custom-reply-status}}\n  WHERE \"type\" = 'SENT' AND \"email_seq_number\" IN ('1', '2', '3')\n  GROUP BY \"leadId\"\n),\nmeeting_agg AS (\n  -- Pre-aggregate meeting data to avoid repeated DISTINCT operations\n  SELECT \n    \"client\",\n    \"date\", \n    \"funnel\",\n    \"fromEmailId\",\n    SUM(\"slots_sent_after_1\") AS total_slots_sent_after_1,\n    <PERSON>UM(\"slots_sent_after_2\") AS total_slots_sent_after_2,\n    <PERSON>UM(\"slots_sent_after_3\") AS total_slots_sent_after_3,\n    <PERSON><PERSON>(\"slots_sent_unknown\") AS total_slots_sent_unknown,\n    SUM(\"booked\") AS total_booked\n  FROM \"public2\".\"MeetingsFromReplit\"\n  GROUP BY \"client\", \"date\", \"funnel\", \"fromEmailId\"\n)\n\nSELECT \n  DATE(emails.\"time\") AS email_date,\n  DATE(DATE(emails.\"time\") - EXTRACT(DOW FROM DATE(emails.\"time\")) * INTERVAL '1 day') AS week_start_date,\n  emails.\"jeff_client_name\",\n  emails.\"jeff_campaign_code\" AS campaign_code,\n  emails.\"clientSmartLeadId\" AS client_id,\n  COALESCE(inboxes.\"provider_name\", 'unknown_provider') AS provider_name,\n  COALESCE(tags.tag, 'no_tag') AS tag_name,\n  \n  -- EMAIL 1 ANALYTICS\n  COUNT(CASE \n    WHEN emails.\"email_seq_number\" = '1' AND emails.\"type\" = 'SENT' \n    THEN 1 \n  END) AS email_1_sent_count,\n  \n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'REPLY' AND flags.has_sent_email_1 = 1\n    THEN 1 \n  END) AS replies_after_email_1_count,\n  \n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'REPLY_CS_AUTOMATED' AND flags.has_sent_email_1 = 1\n    THEN 1 \n  END) AS automated_replies_after_email_1_count,\n\n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'ERROR_REPLY' AND flags.has_sent_email_1 = 1\n    THEN 1 \n  END) AS error_replies_after_email_1_count,\n\n  COALESCE(MAX(meeting_agg.total_slots_sent_after_1), 0) AS meeting_slots_sent_after_1,\n\n  -- EMAIL 2 ANALYTICS\n  COUNT(CASE \n    WHEN emails.\"email_seq_number\" = '2' AND emails.\"type\" = 'SENT' \n    THEN 1 \n  END) AS email_2_sent_count,\n  \n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'REPLY' AND flags.has_sent_email_2 = 1\n    THEN 1 \n  END) AS replies_after_email_2_count,\n  \n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'REPLY_CS_AUTOMATED' AND flags.has_sent_email_2 = 1\n    THEN 1 \n  END) AS automated_replies_after_email_2_count,\n\n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'ERROR_REPLY' AND flags.has_sent_email_2 = 1\n    THEN 1 \n  END) AS error_replies_after_email_2_count,\n\n  COALESCE(MAX(meeting_agg.total_slots_sent_after_2), 0) AS meeting_slots_sent_after_2,\n\n  -- EMAIL 3 ANALYTICS\n  COUNT(CASE \n    WHEN emails.\"email_seq_number\" = '3' AND emails.\"type\" = 'SENT' \n    THEN 1 \n  END) AS email_3_sent_count,\n  \n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'REPLY' AND flags.has_sent_email_3 = 1\n    THEN 1 \n  END) AS replies_after_email_3_count,\n  \n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'REPLY_CS_AUTOMATED' AND flags.has_sent_email_3 = 1\n    THEN 1 \n  END) AS automated_replies_after_email_3_count,\n\n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'ERROR_REPLY' AND flags.has_sent_email_3 = 1\n    THEN 1 \n  END) AS error_replies_after_email_3_count,\n\n  COALESCE(MAX(meeting_agg.total_slots_sent_after_3), 0) AS meeting_slots_sent_after_3,\n\n  -- ADDITIONAL MEETING ANALYTICS\n  COALESCE(MAX(meeting_agg.total_slots_sent_unknown), 0) AS meeting_slots_sent_unknown,\n  \n  COALESCE(MAX(meeting_agg.total_slots_sent_after_1 + meeting_agg.total_slots_sent_after_2 + \n         meeting_agg.total_slots_sent_after_3 + meeting_agg.total_slots_sent_unknown), 0) AS total_meeting_slots_sent,\n  \n  COALESCE(MAX(meeting_agg.total_booked), 0) AS meetings_booked\n\nFROM {{#277-smartlead-emails-with-custom-reply-status}} AS emails\n\n-- Join the pre-calculated flags\nLEFT JOIN email_sent_flags AS flags \n  ON emails.\"leadId\" = flags.\"leadId\"\n\n-- Join to get inbox information\nLEFT JOIN \"public2\".\"InboxesFromReplit\" AS inboxes \n  ON emails.\"threadStartEmailId\" = inboxes.\"inboxEmail\"\n\n-- Join to get inbox tags\nLEFT JOIN \"public2\".\"InboxTagsFromReplit\" AS tags \n  ON inboxes.\"id\" = tags.\"inboxId\"\n\n-- Join to get pre-aggregated meeting data\nLEFT JOIN meeting_agg \n  ON emails.\"clientSmartLeadId\" = meeting_agg.\"client\" \n  AND DATE(emails.\"time\") = meeting_agg.\"date\"\n  AND emails.\"jeff_campaign_code\" = meeting_agg.\"funnel\"\n  AND emails.\"threadStartEmailId\" = meeting_agg.\"fromEmailId\"\n\nGROUP BY \n  DATE(emails.\"time\"),\n  emails.\"jeff_client_name\",\n  emails.\"jeff_campaign_code\",\n  emails.\"clientSmartLeadId\",\n  inboxes.\"provider_name\",\n  tags.tag\n\nORDER BY \n  email_date DESC,\n  emails.\"jeff_client_name\",\n  emails.\"jeff_campaign_code\",\n  client_id,\n  provider_name,\n  tag_name", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 306, "name": "All Prospects (Repeated for all Funnels) with lastSentTime", "display": "table", "query_type": "native", "collection": "Internal Queries", "tables": [{"name": "Email", "schema": "public"}, {"name": "SmartLead_Lead", "schema": "public"}, {"name": "model_base", "schema": "public"}, {"name": "campaign_with_code", "schema": "public"}, {"name": "prospect_cross_campaign", "schema": "public"}, {"name": "public", "schema": "public"}, {"name": "all_campaign_codes", "schema": "public"}, {"name": "model", "schema": "public"}], "sql": "WITH\n  campaign_with_code AS (\n    SELECT\n      \"id\",\n      \"campaignSeries\" as \"campaignCode\"\n    FROM\n      {{#304-campaign-with-funnel-names}} -- this is the model giving campaignCode\n    WHERE\n      \"campaignSeries\" != 'UNK'\n  ),\n  model_base AS (\n    SELECT\n      l.*,\n      COALESCE(\n        (\n          SELECT\n            MAX(e.\"time\")\n          FROM\n            \"Email\" e\n          WHERE\n            e.\"leadId\" = l.\"id\"\n        ),\n        l.\"createdAt\" + INTERVAL '7 days'\n      ) AS \"lastSentTimeAt\",\n      cwc.\"campaignCode\",\n      EXISTS (\n        SELECT\n          1\n        FROM\n          \"Email\" e\n        WHERE\n          e.\"leadId\" = l.\"id\"\n      ) AS \"hasEmail\",\n      c.\"name\" AS \"campaignName\"\n    FROM\n      \"SmartLead_Lead\" l\n      JOIN public.\"Campaign\" c ON c.\"id\" = l.\"campaignId\"\n      JOIN campaign_with_code cwc ON c.\"id\" = cwc.\"id\"\n  ),\n  model AS (\n    SELECT *\n    FROM (\n      SELECT *,\n        ROW_NUMBER() OVER (\n          PARTITION BY LOWER(email), \"campaignCode\" \n          ORDER BY \"lastSentTimeAt\" DESC\n        ) as rn\n      FROM model_base\n    ) ranked\n    WHERE rn = 1\n  ),\n  all_campaign_codes AS (\n    SELECT DISTINCT\n      \"campaignCode\"\n    FROM\n      campaign_with_code\n  ),\n  prospect_cross_campaign AS (\n    SELECT\n      p.*,\n      acc.\"campaignCode\"\n    FROM\n      {{#117-sellers-with-sp-classifications-prospects-w-emails}} p\n      CROSS JOIN all_campaign_codes acc\n  )\nSELECT\n  pc.*,\n  m.\"lastSentTimeAt\",\n  m.\"campaignName\",\n  m.\"hasEmail\"\nFROM\n  prospect_cross_campaign pc\n  LEFT JOIN model m ON LOWER(pc.\"Prospects with eMail Classification__email\") = LOWER(m.email)\n  AND pc.\"campaignCode\" = m.\"campaignCode\";", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 313, "name": "Smartlead Analytics (by Provider & Client)", "display": "table", "query_type": "native", "collection": "Internal Queries", "tables": [{"name": "DATE", "schema": "public"}, {"name": "enriched_emails", "schema": "public"}, {"name": "InboxesFromReplit", "schema": "public2"}, {"name": "lead_email_flags", "schema": "public"}, {"name": "public2", "schema": "public"}], "sql": "WITH lead_email_flags AS (\n  SELECT \n    \"leadId\",\n    MAX(CASE WHEN \"email_seq_number\" = '1' AND \"type\" = 'SENT' THEN 1 ELSE 0 END) AS has_email_1_sent,\n    MAX(CASE WHEN \"email_seq_number\" = '2' AND \"type\" = 'SENT' THEN 1 ELSE 0 END) AS has_email_2_sent,\n    MAX(CASE WHEN \"email_seq_number\" = '3' AND \"type\" = 'SENT' THEN 1 ELSE 0 END) AS has_email_3_sent\n  FROM {{#277-smartlead-emails-with-custom-reply-status}}\n  GROUP BY \"leadId\"\n),\nenriched_emails AS (\n  SELECT \n    emails.*,\n    flags.has_email_1_sent,\n    flags.has_email_2_sent,\n    flags.has_email_3_sent,\n    COALESCE(inboxes.\"provider_name\", 'unknown_provider') AS provider_name\n  FROM {{#277-smartlead-emails-with-custom-reply-status}} AS emails\n  LEFT JOIN lead_email_flags AS flags ON emails.\"leadId\" = flags.\"leadId\"\n  LEFT JOIN \"public2\".\"InboxesFromReplit\" AS inboxes \n    ON emails.\"threadStartEmailId\" = inboxes.\"inboxEmail\"\n)\n\nSELECT \n  DATE(emails.\"time\") AS email_date,\n  DATE(DATE(emails.\"time\") - EXTRACT(DOW FROM DATE(emails.\"time\")) * INTERVAL '1 day') AS week_start_date,\n  emails.\"jeff_client_name\",\n  emails.\"jeff_campaign_code\" AS campaign_code,\n  emails.\"clientSmartLeadId\" AS client_id,\n  emails.provider_name,\n  \n  -- EMAIL 1 ANALYTICS\n  COUNT(CASE \n    WHEN emails.\"email_seq_number\" = '1' AND emails.\"type\" = 'SENT' \n    THEN 1 \n  END) AS email_1_sent_count,\n  \n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'REPLY' AND emails.has_email_1_sent = 1\n    THEN 1 \n  END) AS replies_after_email_1_count,\n  \n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'REPLY_CS_AUTOMATED' AND emails.has_email_1_sent = 1\n    THEN 1 \n  END) AS automated_replies_after_email_1_count,\n\n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'ERROR_REPLY' AND emails.has_email_1_sent = 1\n    THEN 1 \n  END) AS error_replies_after_email_1_count,\n\n  -- EMAIL 2 ANALYTICS\n  COUNT(CASE \n    WHEN emails.\"email_seq_number\" = '2' AND emails.\"type\" = 'SENT' \n    THEN 1 \n  END) AS email_2_sent_count,\n  \n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'REPLY' AND emails.has_email_2_sent = 1\n    THEN 1 \n  END) AS replies_after_email_2_count,\n  \n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'REPLY_CS_AUTOMATED' AND emails.has_email_2_sent = 1\n    THEN 1 \n  END) AS automated_replies_after_email_2_count,\n\n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'ERROR_REPLY' AND emails.has_email_2_sent = 1\n    THEN 1 \n  END) AS error_replies_after_email_2_count,\n\n  -- EMAIL 3 ANALYTICS\n  COUNT(CASE \n    WHEN emails.\"email_seq_number\" = '3' AND emails.\"type\" = 'SENT' \n    THEN 1 \n  END) AS email_3_sent_count,\n  \n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'REPLY' AND emails.has_email_3_sent = 1\n    THEN 1 \n  END) AS replies_after_email_3_count,\n  \n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'REPLY_CS_AUTOMATED' AND emails.has_email_3_sent = 1\n    THEN 1 \n  END) AS automated_replies_after_email_3_count,\n\n  COUNT(CASE \n    WHEN emails.\"jeff_email_status\" = 'ERROR_REPLY' AND emails.has_email_3_sent = 1\n    THEN 1 \n  END) AS error_replies_after_email_3_count\n\nFROM enriched_emails AS emails\n\nGROUP BY \n  DATE(emails.\"time\"),\n  emails.\"jeff_client_name\",\n  emails.\"jeff_campaign_code\",\n  emails.\"clientSmartLeadId\",\n  emails.provider_name\n\nORDER BY \n  email_date DESC,\n  emails.\"jeff_client_name\",\n  emails.\"jeff_campaign_code\",\n  client_id,\n  provider_name", "source_query": null, "aggregations": [], "filters": [], "breakouts": []}, {"id": 364, "name": "# Smartlead of Errors in Last 60 Days", "display": "scalar", "query_type": "query", "collection": "Download Jeff <PERSON>s", "tables": [], "sql": null, "source_query": 277, "aggregations": [], "filters": ["and", ["=", ["field", "jeff_email_status", {"base-type": "type/Text"}], "ERROR_REPLY"], ["time-interval", ["field", "time", {"base-type": "type/DateTime"}], -60, "day"]], "breakouts": []}], "summary": {"total_queries": 60, "native_sql_queries": 20, "gui_queries": 40, "tables_used": ["status_options", "public", "status_counts", "all_combinations", "priority_options", "actual_counts", "companies_with_apollo_prospects", "companies_with_harvest_prospects", "companies_with_amazon_sf_prospects", "Phase", "companies_with_apollo_p2_prospects", "companies_with_apollo_p1_prospects", "companies_with_apollo_p3_prospects", "companies_with_anymail_prospects", "FunnelClientAnalytics", "public2", "MeetingsFromReplit", "Email", "email_last_sent", "DATE", "email_analytics_base", "lead_sequences", "InboxesFromReplit", "InboxTagsFromReplit", "the", "email_sent_flags", "to", "meeting_agg", "SmartLead_Lead", "model_base", "campaign_with_code", "prospect_cross_campaign", "all_campaign_codes", "model", "enriched_emails", "lead_email_flags"], "queries_with_dependencies": [{"query": 112, "depends_on": 117}, {"query": 113, "depends_on": 119}, {"query": 134, "depends_on": 119}, {"query": 149, "depends_on": 119}, {"query": 150, "depends_on": 119}, {"query": 152, "depends_on": 117}, {"query": 188, "depends_on": 186}, {"query": 190, "depends_on": 186}, {"query": 282, "depends_on": 117}, {"query": 291, "depends_on": 278}, {"query": 290, "depends_on": 289}, {"query": 310, "depends_on": 306}, {"query": 311, "depends_on": 297}, {"query": 312, "depends_on": 297}, {"query": 314, "depends_on": 313}, {"query": 365, "depends_on": 364}, {"query": 117, "depends_on": 119}, {"query": 364, "depends_on": 277}]}}