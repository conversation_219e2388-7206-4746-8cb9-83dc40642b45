/**
 * Step 5: Refresh the MV (run this periodically)
 * Run: node analytics_v2_with_sp/5_refreshMV.js
 * 
 * Add to cron for automatic refresh every 15 minutes:
 * */15 * * * * cd /path/to/sellerbot && node analytics_v2_with_sp/5_refreshMV.js
 */

require("dotenv").config({ path: "../.env" });
const prisma = require("../database/prisma/getPrismaClient");

async function refreshMV() {
  const startTime = Date.now();
  
  try {
    // Determine which MV to refresh based on what exists
    const mvCheck = await prisma.$queryRawUnsafe(`
      SELECT matviewname 
      FROM pg_matviews 
      WHERE matviewname IN ('mv_email_with_thread_start', 'mv_email_with_thread_start_v2')
    `);
    
    const mvNames = mvCheck.map(row => row.matviewname);
    
    // Refresh the v2 if it exists and hasn't been renamed yet
    if (mvNames.includes('mv_email_with_thread_start_v2')) {
      console.log('🔄 Refreshing mv_email_with_thread_start_v2...');
      await prisma.$executeRawUnsafe('REFRESH MATERIALIZED VIEW mv_email_with_thread_start_v2');
      console.log('✅ Refreshed v2 MV');
    }
    
    // Also refresh the main MV if it has SP (check if sp column exists)
    if (mvNames.includes('mv_email_with_thread_start')) {
      const hasSpColumn = await prisma.$queryRawUnsafe(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'mv_email_with_thread_start' 
        AND column_name = 'sp'
        LIMIT 1
      `);
      
      if (hasSpColumn.length > 0) {
        console.log('🔄 Refreshing mv_email_with_thread_start (with SP)...');
        await prisma.$executeRawUnsafe('REFRESH MATERIALIZED VIEW mv_email_with_thread_start');
        console.log('✅ Refreshed main MV with SP');
      } else {
        console.log('ℹ️  Main MV does not have SP yet (still using original)');
      }
    }
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(`\n⏱️  Refresh completed in ${duration} seconds`);
    
    // Show current stats
    const stats = await prisma.$queryRawUnsafe(`
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE sp != 'UNK') as with_sp,
        COUNT(*) FILTER (WHERE sp = 'UNK') as without_sp
      FROM ${mvNames.includes('mv_email_with_thread_start_v2') ? 'mv_email_with_thread_start_v2' : 'mv_email_with_thread_start'}
    `);
    
    if (stats[0]) {
      console.log('\n📊 Current Stats:');
      console.log(`   Total records: ${stats[0].total}`);
      if (stats[0].with_sp !== null) {
        console.log(`   With SP: ${stats[0].with_sp}`);
        console.log(`   Without SP (UNK): ${stats[0].without_sp}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error refreshing MV:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🚀 Refreshing Analytics MV');
  console.log(`📅 ${new Date().toISOString()}`);
  console.log('=' .repeat(60));
  
  try {
    await refreshMV();
    console.log('\n✅ Refresh complete');
  } catch (error) {
    console.error('Failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();