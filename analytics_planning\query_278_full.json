{"cache_invalidated_at": "2025-09-04T18:14:10.514097Z", "description": null, "archived": false, "view_count": 102, "collection_position": 2, "source_card_id": null, "table_id": null, "can_run_adhoc_query": true, "result_metadata": [{"display_name": "email_date", "field_ref": ["field", "email_date", {"base-type": "type/Date"}], "base_type": "type/Date", "effective_type": "type/Date", "database_type": "date", "name": "email_date", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 162, "nil%": 0}, "type": {"type/DateTime": {"earliest": "2025-01-14", "latest": "2025-06-25"}}}}, {"display_name": "week_start_date", "semantic_type": "type/CreationDate", "field_ref": ["field", "week_start_date", {"base-type": "type/Date"}], "base_type": "type/Date", "effective_type": "type/Date", "database_type": "date", "name": "week_start_date", "fingerprint": {"global": {"distinct-count": 24, "nil%": 0}, "type": {"type/DateTime": {"earliest": "2025-01-12", "latest": "2025-06-22"}}}}, {"display_name": "jeff_client_name", "field_ref": ["field", "jeff_client_name", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "jeff_client_name", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 10, "nil%": 0.05310229178311906}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 8.26774734488541}}}}, {"display_name": "campaign_code", "field_ref": ["field", "campaign_code", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "campaign_code", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 8, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 2.2895472330911124}}}}, {"display_name": "client_id", "field_ref": ["field", "client_id", {"base-type": "type/Integer"}], "base_type": "type/Integer", "effective_type": "type/Integer", "database_type": "int4", "name": "client_id", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 10, "nil%": 0.05310229178311906}, "type": {"type/Number": {"min": 25946, "q1": 25948.047517943556, "q3": 36726.062628542255, "max": 75224, "sd": 9183.222936928538, "avg": 32130.10507674144}}}}, {"display_name": "email_1_sent_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_1_sent_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_1_sent_count", "fingerprint": {"global": {"distinct-count": 459, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.6323262426700557, "q3": 249.40292910847947, "max": 1577, "sd": 201.9144587101656, "avg": 142.217998882057}}}}, {"display_name": "email_2_sent_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_2_sent_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_2_sent_count", "fingerprint": {"global": {"distinct-count": 416, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.6771313120050388, "q3": 191.5759130903883, "max": 1555, "sd": 159.6605145206814, "avg": 111.83007266629401}}}}, {"display_name": "email_3_sent_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_3_sent_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_3_sent_count", "fingerprint": {"global": {"distinct-count": 286, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.2922069177573866, "q3": 97.82662968787892, "max": 980, "sd": 103.50556071256518, "avg": 58.62493012856344}}}}, {"display_name": "email_1_sent_personal_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_1_sent_personal_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_1_sent_personal_count", "fingerprint": {"global": {"distinct-count": 70, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.0610802369868505, "q3": 10.575325896445694, "max": 139, "sd": 13.182445096783104, "avg": 7.287311347121297}}}}, {"display_name": "email_1_sent_role_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_1_sent_role_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_1_sent_role_count", "fingerprint": {"global": {"distinct-count": 262, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.1788301594509554, "q3": 94.72306490866627, "max": 749, "sd": 82.18233343997335, "avg": 51.91503633314701}}}}, {"display_name": "email_1_sent_work_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_1_sent_work_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_1_sent_work_count", "fingerprint": {"global": {"distinct-count": 317, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.1470331012262433, "q3": 106.37289734898599, "max": 907, "sd": 108.37020385119004, "avg": 65.2375628842929}}}}, {"display_name": "email_1_sent_unknown_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_1_sent_unknown_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_1_sent_unknown_count", "fingerprint": {"global": {"distinct-count": 132, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.3432756501476309, "q3": 2.688964109842374, "max": 881, "sd": 71.13236882807651, "avg": 17.778088317495808}}}}, {"display_name": "email_2_sent_personal_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_2_sent_personal_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_2_sent_personal_count", "fingerprint": {"global": {"distinct-count": 61, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 8.590661511256267, "max": 65, "sd": 10.627546488016444, "avg": 6.124650642817216}}}}, {"display_name": "email_2_sent_role_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_2_sent_role_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_2_sent_role_count", "fingerprint": {"global": {"distinct-count": 228, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.295065537351059, "q3": 68.09491198383927, "max": 613, "sd": 64.5136014399044, "avg": 40.63666852990497}}}}, {"display_name": "email_2_sent_work_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_2_sent_work_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_2_sent_work_count", "fingerprint": {"global": {"distinct-count": 279, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.25501782318750443, "q3": 79.47784272663996, "max": 877, "sd": 85.94292800218133, "avg": 52.39575181665735}}}}, {"display_name": "email_2_sent_unknown_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_2_sent_unknown_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_2_sent_unknown_count", "fingerprint": {"global": {"distinct-count": 122, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.2432204883247253, "q3": 2.105227987522788, "max": 537, "sd": 50.63014708614562, "avg": 12.673001676914478}}}}, {"display_name": "email_3_sent_personal_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_3_sent_personal_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_3_sent_personal_count", "fingerprint": {"global": {"distinct-count": 51, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 3.379356345914787, "max": 98, "sd": 8.040968872451662, "avg": 3.6008943543879264}}}}, {"display_name": "email_3_sent_role_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_3_sent_role_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_3_sent_role_count", "fingerprint": {"global": {"distinct-count": 163, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.05843740640509552, "q3": 30.438426647416136, "max": 329, "sd": 40.713207098562634, "avg": 21.173281162660704}}}}, {"display_name": "email_3_sent_work_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_3_sent_work_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_3_sent_work_count", "fingerprint": {"global": {"distinct-count": 201, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.04442272344942523, "q3": 36.58602294078788, "max": 548, "sd": 55.71141001719864, "avg": 28.248183342649526}}}}, {"display_name": "email_3_sent_unknown_count", "semantic_type": "type/Quantity", "field_ref": ["field", "email_3_sent_unknown_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "email_3_sent_unknown_count", "fingerprint": {"global": {"distinct-count": 79, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.07123220467313728, "q3": 1.0647946803608759, "max": 340, "sd": 28.425651196754533, "avg": 5.602571268865288}}}}, {"display_name": "replies_after_email_1_count", "semantic_type": "type/Quantity", "field_ref": ["field", "replies_after_email_1_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "replies_after_email_1_count", "fingerprint": {"global": {"distinct-count": 22, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.15923053890615912, "q3": 3.5983305511630626, "max": 23, "sd": 3.5726089102385825, "avg": 2.505310229178312}}}}, {"display_name": "replies_after_email_2_count", "semantic_type": "type/Quantity", "field_ref": ["field", "replies_after_email_2_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "replies_after_email_2_count", "fingerprint": {"global": {"distinct-count": 10, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 1.1533544069338024, "max": 9, "sd": 1.3079513100224318, "avg": 0.7529346003353828}}}}, {"display_name": "replies_after_email_3_count", "semantic_type": "type/Quantity", "field_ref": ["field", "replies_after_email_3_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "replies_after_email_3_count", "fingerprint": {"global": {"distinct-count": 7, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 0.5181105559055353, "max": 7, "sd": 0.6610271508563885, "avg": 0.2543320290665176}}}}, {"display_name": "automated_replies_after_email_1_count", "semantic_type": "type/Quantity", "field_ref": ["field", "automated_replies_after_email_1_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "automated_replies_after_email_1_count", "fingerprint": {"global": {"distinct-count": 36, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.05968774207205222, "q3": 5.180476215019562, "max": 60, "sd": 5.5951684484762705, "avg": 3.5125768585802124}}}}, {"display_name": "automated_replies_after_email_2_count", "semantic_type": "type/Quantity", "field_ref": ["field", "automated_replies_after_email_2_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "automated_replies_after_email_2_count", "fingerprint": {"global": {"distinct-count": 11, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 0.7600646761981339, "max": 12, "sd": 1.160038568089973, "avg": 0.5254332029066517}}}}, {"display_name": "automated_replies_after_email_3_count", "semantic_type": "type/Quantity", "field_ref": ["field", "automated_replies_after_email_3_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "automated_replies_after_email_3_count", "fingerprint": {"global": {"distinct-count": 5, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 0.34933863314291475, "max": 4, "sd": 0.30799161971469935, "avg": 0.06428172163219675}}}}, {"display_name": "error_replies_after_email_1_count", "semantic_type": "type/Quantity", "field_ref": ["field", "error_replies_after_email_1_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "error_replies_after_email_1_count", "fingerprint": {"global": {"distinct-count": 39, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0.00943294067596404, "q3": 5.455770490487787, "max": 65, "sd": 6.194541097974328, "avg": 3.6523197316936837}}}}, {"display_name": "error_replies_after_email_2_count", "semantic_type": "type/Quantity", "field_ref": ["field", "error_replies_after_email_2_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "error_replies_after_email_2_count", "fingerprint": {"global": {"distinct-count": 11, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 0.6116897713991473, "max": 13, "sd": 1.0877204110055856, "avg": 0.38960313024035775}}}}, {"display_name": "error_replies_after_email_3_count", "semantic_type": "type/Quantity", "field_ref": ["field", "error_replies_after_email_3_count", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "error_replies_after_email_3_count", "fingerprint": {"global": {"distinct-count": 5, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 0.3493926140159432, "max": 10, "sd": 0.37304006716549953, "avg": 0.06763555058692007}}}}, {"display_name": "meeting_slots_sent_after_1", "field_ref": ["field", "meeting_slots_sent_after_1", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "meeting_slots_sent_after_1", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 4, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 0.38802213527390644, "max": 3, "sd": 0.2892274577421136, "avg": 0.08664058133035216}}}}, {"display_name": "meeting_slots_sent_after_2", "field_ref": ["field", "meeting_slots_sent_after_2", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "meeting_slots_sent_after_2", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 3, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 0.3347370805547096, "max": 2, "sd": 0.19941510080492275, "avg": 0.04024594745667971}}}}, {"display_name": "meeting_slots_sent_after_3", "field_ref": ["field", "meeting_slots_sent_after_3", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "meeting_slots_sent_after_3", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 3, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 0.31007074861343076, "max": 2, "sd": 0.1347439953872753, "avg": 0.017328116266070427}}}}, {"display_name": "meeting_slots_sent_unknown", "field_ref": ["field", "meeting_slots_sent_unknown", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "meeting_slots_sent_unknown", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 1, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 0, "max": 0, "sd": 0, "avg": 0}}}}, {"display_name": "total_meeting_slots_sent", "field_ref": ["field", "total_meeting_slots_sent", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "total_meeting_slots_sent", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 4, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 0.4450681802660386, "max": 3, "sd": 0.401891933436392, "avg": 0.1442146450531023}}}}, {"display_name": "meetings_booked", "field_ref": ["field", "meetings_booked", {"base-type": "type/BigInteger"}], "base_type": "type/BigInteger", "effective_type": "type/BigInteger", "database_type": "int8", "name": "meetings_booked", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 3, "nil%": 0}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 0.34107899883373527, "max": 2, "sd": 0.21938481349490763, "avg": 0.046953605366126326}}}}], "creator": {"email": "<EMAIL>", "first_name": "TechTeam", "last_login": "2025-09-11T14:20:40.840338Z", "is_qbnewb": false, "is_superuser": true, "id": 2, "last_name": "Admin", "date_joined": "2024-09-30T18:52:12.583269Z", "common_name": "TechTeam Admin"}, "initially_published_at": null, "can_write": true, "database_id": 4, "enable_embedding": false, "collection_id": 50, "query_type": "native", "name": "Smartlead Analytics", "last_query_start": "2025-06-30T09:13:55.280959Z", "dashboard_count": 0, "last_used_at": "2025-08-27T09:38:13.617521Z", "dashboard": null, "type": "model", "persisted": false, "average_query_time": 19470.938271604937, "creator_id": 2, "can_restore": false, "moderation_reviews": [], "updated_at": "2025-09-04T18:14:10.601546Z", "made_public_by_id": null, "embedding_params": null, "cache_ttl": null, "dataset_query": {"database": 4, "info": {"card-entity-id": "nCovBwXNFeo8Fl9C1hK_Z"}, "type": "native", "native": {"template-tags": {"#277-smartlead-emails-with-custom-reply-status": {"type": "card", "name": "#277-smartlead-emails-with-custom-reply-status", "id": "6fed52e2-c52b-46fb-99b2-567cdef8da8b", "card-id": 277, "display-name": "#277 Smartlead Emails With Custom Reply Status"}}, "query": "WITH email_analytics_base AS (\n  SELECT \n    DATE(emails.\"time\") AS email_date,\n    DATE(DATE(emails.\"time\") - EXTRACT(DOW FROM DATE(emails.\"time\")) * INTERVAL '1 day') AS week_start_date,\n    emails.\"jeff_client_name\",\n    emails.\"jeff_campaign_code\",\n    emails.\"clientSmartLeadId\",\n    emails.\"leadId\",\n    emails.\"email_seq_number\",\n    emails.\"type\",\n    emails.\"jeff_email_status\",\n    emails.\"threadStartEmailId\",\n    emails.\"prospect_email_type\",\n    \n    -- Create flags for email sequences sent (avoids multiple scans)\n    CASE WHEN emails.\"email_seq_number\" = '1' AND emails.\"type\" = 'SENT' THEN 1 ELSE 0 END AS email_1_sent,\n    CASE WHEN emails.\"email_seq_number\" = '2' AND emails.\"type\" = 'SENT' THEN 1 ELSE 0 END AS email_2_sent,\n    CASE WHEN emails.\"email_seq_number\" = '3' AND emails.\"type\" = 'SENT' THEN 1 ELSE 0 END AS email_3_sent,\n    \n    -- Create flags for email sequences sent by prospect type\n    -- Email 1\n    CASE WHEN emails.\"email_seq_number\" = '1' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'personal' THEN 1 ELSE 0 END AS email_1_sent_personal,\n    CASE WHEN emails.\"email_seq_number\" = '1' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'role' THEN 1 ELSE 0 END AS email_1_sent_role,\n    CASE WHEN emails.\"email_seq_number\" = '1' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'work' THEN 1 ELSE 0 END AS email_1_sent_work,\n    CASE WHEN emails.\"email_seq_number\" = '1' AND emails.\"type\" = 'SENT' AND (emails.\"prospect_email_type\" IS NULL OR emails.\"prospect_email_type\" = '') THEN 1 ELSE 0 END AS email_1_sent_unknown,\n    \n    -- Email 2\n    CASE WHEN emails.\"email_seq_number\" = '2' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'personal' THEN 1 ELSE 0 END AS email_2_sent_personal,\n    CASE WHEN emails.\"email_seq_number\" = '2' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'role' THEN 1 ELSE 0 END AS email_2_sent_role,\n    CASE WHEN emails.\"email_seq_number\" = '2' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'work' THEN 1 ELSE 0 END AS email_2_sent_work,\n    CASE WHEN emails.\"email_seq_number\" = '2' AND emails.\"type\" = 'SENT' AND (emails.\"prospect_email_type\" IS NULL OR emails.\"prospect_email_type\" = '') THEN 1 ELSE 0 END AS email_2_sent_unknown,\n    \n    -- Email 3\n    CASE WHEN emails.\"email_seq_number\" = '3' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'personal' THEN 1 ELSE 0 END AS email_3_sent_personal,\n    CASE WHEN emails.\"email_seq_number\" = '3' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'role' THEN 1 ELSE 0 END AS email_3_sent_role,\n    CASE WHEN emails.\"email_seq_number\" = '3' AND emails.\"type\" = 'SENT' AND emails.\"prospect_email_type\" = 'work' THEN 1 ELSE 0 END AS email_3_sent_work,\n    CASE WHEN emails.\"email_seq_number\" = '3' AND emails.\"type\" = 'SENT' AND (emails.\"prospect_email_type\" IS NULL OR emails.\"prospect_email_type\" = '') THEN 1 ELSE 0 END AS email_3_sent_unknown,\n    \n    -- Create flags for replies\n    CASE WHEN emails.\"jeff_email_status\" = 'REPLY' THEN 1 ELSE 0 END AS is_reply,\n    CASE WHEN emails.\"jeff_email_status\" = 'REPLY_CS_AUTOMATED' THEN 1 ELSE 0 END AS is_auto_reply,\n    CASE WHEN emails.\"jeff_email_status\" = 'ERROR_REPLY' THEN 1 ELSE 0 END AS is_error_reply\n    \n  FROM {{#277-smartlead-emails-with-custom-reply-status}} AS emails\n),\nlead_sequences AS (\n  SELECT \n    \"leadId\",\n    MAX(email_1_sent) AS has_email_1_sent,\n    MAX(email_2_sent) AS has_email_2_sent,\n    MAX(email_3_sent) AS has_email_3_sent\n  FROM email_analytics_base\n  GROUP BY \"leadId\"\n)\n\nSELECT \n  eab.email_date,\n  eab.week_start_date,\n  eab.\"jeff_client_name\",\n  eab.\"jeff_campaign_code\" AS campaign_code,\n  eab.\"clientSmartLeadId\" AS client_id,\n  \n  -- Email counts (total)\n  SUM(eab.email_1_sent) AS email_1_sent_count,\n  SUM(eab.email_2_sent) AS email_2_sent_count,\n  SUM(eab.email_3_sent) AS email_3_sent_count,\n  \n  -- Email 1 counts by prospect type\n  SUM(eab.email_1_sent_personal) AS email_1_sent_personal_count,\n  SUM(eab.email_1_sent_role) AS email_1_sent_role_count,\n  SUM(eab.email_1_sent_work) AS email_1_sent_work_count,\n  SUM(eab.email_1_sent_unknown) AS email_1_sent_unknown_count,\n  \n  -- Email 2 counts by prospect type\n  SUM(eab.email_2_sent_personal) AS email_2_sent_personal_count,\n  SUM(eab.email_2_sent_role) AS email_2_sent_role_count,\n  SUM(eab.email_2_sent_work) AS email_2_sent_work_count,\n  SUM(eab.email_2_sent_unknown) AS email_2_sent_unknown_count,\n  \n  -- Email 3 counts by prospect type\n  SUM(eab.email_3_sent_personal) AS email_3_sent_personal_count,\n  SUM(eab.email_3_sent_role) AS email_3_sent_role_count,\n  SUM(eab.email_3_sent_work) AS email_3_sent_work_count,\n  SUM(eab.email_3_sent_unknown) AS email_3_sent_unknown_count,\n  \n  -- Reply counts using pre-calculated flags\n  SUM(CASE WHEN eab.is_reply = 1 AND ls.has_email_1_sent = 1 THEN 1 ELSE 0 END) AS replies_after_email_1_count,\n  SUM(CASE WHEN eab.is_reply = 1 AND ls.has_email_2_sent = 1 THEN 1 ELSE 0 END) AS replies_after_email_2_count,\n  SUM(CASE WHEN eab.is_reply = 1 AND ls.has_email_3_sent = 1 THEN 1 ELSE 0 END) AS replies_after_email_3_count,\n  \n  -- Auto reply counts\n  SUM(CASE WHEN eab.is_auto_reply = 1 AND ls.has_email_1_sent = 1 THEN 1 ELSE 0 END) AS automated_replies_after_email_1_count,\n  SUM(CASE WHEN eab.is_auto_reply = 1 AND ls.has_email_2_sent = 1 THEN 1 ELSE 0 END) AS automated_replies_after_email_2_count,\n  SUM(CASE WHEN eab.is_auto_reply = 1 AND ls.has_email_3_sent = 1 THEN 1 ELSE 0 END) AS automated_replies_after_email_3_count,\n  \n  -- Error reply counts  \n  SUM(CASE WHEN eab.is_error_reply = 1 AND ls.has_email_1_sent = 1 THEN 1 ELSE 0 END) AS error_replies_after_email_1_count,\n  SUM(CASE WHEN eab.is_error_reply = 1 AND ls.has_email_2_sent = 1 THEN 1 ELSE 0 END) AS error_replies_after_email_2_count,\n  SUM(CASE WHEN eab.is_error_reply = 1 AND ls.has_email_3_sent = 1 THEN 1 ELSE 0 END) AS error_replies_after_email_3_count,\n  \n  -- Meeting analytics (simplified)\n  SUM(DISTINCT COALESCE(meetings.slots_sent_after_1, 0)) AS meeting_slots_sent_after_1,\n  SUM(DISTINCT COALESCE(meetings.slots_sent_after_2, 0)) AS meeting_slots_sent_after_2,\n  SUM(DISTINCT COALESCE(meetings.slots_sent_after_3, 0)) AS meeting_slots_sent_after_3,\n  SUM(DISTINCT COALESCE(meetings.slots_sent_unknown, 0)) AS meeting_slots_sent_unknown,\n  (SUM(DISTINCT COALESCE(meetings.slots_sent_after_1, 0)) + \n   SUM(DISTINCT COALESCE(meetings.slots_sent_after_2, 0)) + \n   SUM(DISTINCT COALESCE(meetings.slots_sent_after_3, 0)) + \n   SUM(DISTINCT COALESCE(meetings.slots_sent_unknown, 0))) AS total_meeting_slots_sent,\n  SUM(DISTINCT COALESCE(meetings.booked, 0)) AS meetings_booked\n\nFROM email_analytics_base eab\nLEFT JOIN lead_sequences ls ON eab.\"leadId\" = ls.\"leadId\"\nLEFT JOIN \"public2\".\"MeetingsFromReplit\" AS meetings \n  ON eab.\"clientSmartLeadId\" = meetings.\"client\" \n  AND eab.email_date = meetings.\"date\"\n  AND eab.\"jeff_campaign_code\" = meetings.\"funnel\"\n  AND eab.\"threadStartEmailId\" = meetings.\"fromEmailId\"\n\nGROUP BY \n  eab.email_date,\n  eab.week_start_date,\n  eab.\"jeff_client_name\",\n  eab.\"jeff_campaign_code\",\n  eab.\"clientSmartLeadId\"\n\nORDER BY \n  eab.email_date DESC,\n  eab.\"jeff_client_name\",\n  eab.\"jeff_campaign_code\",\n  eab.\"clientSmartLeadId\";"}}, "id": 278, "parameter_mappings": [], "can_manage_db": true, "display": "table", "archived_directly": false, "entity_id": "kLOFJ-oKMN_yksoGqWDdA", "collection_preview": true, "last-edit-info": {"id": 2, "email": "<EMAIL>", "first_name": "TechTeam", "last_name": "Admin", "timestamp": "2025-09-04T18:14:10.529003Z"}, "visualization_settings": {"table.pivot_column": "automated_replies_after_email_3_count", "table.cell_column": "email_1_sent_count"}, "collection": {"authority_level": null, "description": "Internal Queries queries for <PERSON> Seller (Dashboard)", "archived": false, "slug": "internal_queries", "archive_operation_id": null, "name": "Internal Queries", "personal_owner_id": null, "type": null, "is_sample": false, "id": 50, "archived_directly": null, "entity_id": "CXcl32Tbov-N2JckQNdGe", "location": "/43/", "namespace": null, "is_personal": false, "created_at": "2025-09-04T18:14:03.724182Z"}, "metabase_version": "v0.54.5.4 (f262d88)", "parameters": [], "dashboard_id": null, "created_at": "2025-06-11T16:07:51.679702Z", "parameter_usage_count": 0, "public_uuid": null, "can_delete": false}