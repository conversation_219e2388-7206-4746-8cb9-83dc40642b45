# Table Creation Logic - InboxesFromReplit and MeetingsFromReplit

## InboxesFromReplit Table Creation

### Source
- **API Endpoint**: https://jeffcrm.equalcollective.com/api/public/inbox-tags
- **Purpose**: Store inbox email configurations with their provider information

### Table Structure
```sql
CREATE TABLE IF NOT EXISTS "public2"."InboxesFromReplit" (
  "id" SERIAL PRIMARY KEY,
  "inboxEmail" TEXT UNIQUE NOT NULL,
  "provider_name" TEXT
)
```

### Creation Process
1. **Fetch Data**: GET request to CRM API /inbox-tags endpoint
2. **Truncate**: Clear existing data with `TRUNCATE TABLE "public2"."InboxesFromReplit" CASCADE`
3. **Process Data**:
   - Extract email address from each inbox
   - Extract provider name from `inbox.domain?.currentProvider?.name`
   - Process in batches of 500 for performance
4. **Insert**: 
   - Use `INSERT ... ON CONFLICT ("inboxEmail") DO UPDATE`
   - Updates provider_name if email already exists
5. **Tag Processing**: After inboxes inserted, process associated tags into InboxTagsFromReplit

### Data Flow
```
CRM API (/inbox-tags) → Extract email & provider → Batch insert → InboxesFromReplit
```

---

## MeetingsFromReplit Table Creation

### Source
- **API Endpoint**: https://jeffcrm.equalcollective.com/api/public/meetings
- **Purpose**: Store aggregated meeting metrics by date, client, funnel, and email sender

### Table Structure
```sql
CREATE TABLE IF NOT EXISTS "public2"."MeetingsFromReplit" (
  "id" SERIAL PRIMARY KEY,
  "date" DATE NOT NULL,
  "client" INTEGER NOT NULL,
  "funnel" TEXT NOT NULL,
  "fromEmailId" TEXT NOT NULL,
  "slots_sent_after_1" INTEGER DEFAULT 0,
  "slots_sent_after_2" INTEGER DEFAULT 0,
  "slots_sent_after_3" INTEGER DEFAULT 0,
  "slots_sent_unknown" INTEGER DEFAULT 0,
  "booked" INTEGER DEFAULT 0
)
```

### Creation Process

1. **Fetch Data**: GET request to CRM API /meetings endpoint

2. **Truncate**: Clear existing data with `TRUNCATE TABLE "public2"."MeetingsFromReplit" CASCADE`

3. **Process Each Meeting**:
   - **Extract fromEmailId**: 
     - Look through emailHistory array
     - Find emails with type='SENT'
     - Take the 'from' field of the earliest sent email
     - Default to 'unknown' if no sent emails
   
   - **Extract Client**: `meeting.client?.smartleadId || "UNK"`
   
   - **Extract Funnel**: `meeting.campaignCategory?.code || "UNK"`
   
   - **Determine Email Sequence**:
     - Parse emailHistory to find max email_seq_number
     - Convert to integer (handles string values)
     - Used to categorize slots_sent (after email 1, 2, 3, or unknown)

4. **Aggregate by Key**:
   - Create key: `${date}|${clientId}|${funnel}|${fromEmailId}`
   - For each unique combination, count:
     - slots_sent_after_1 (if max seq = 1)
     - slots_sent_after_2 (if max seq = 2)
     - slots_sent_after_3 (if max seq = 3)
     - slots_sent_unknown (if max seq = 0 or undefined)
     - booked (when stage = 'booked')

5. **Insert**: Batch insert aggregated data (500 records per batch)

### Key Logic Points

#### Email Sequence Attribution
```javascript
// Determine which email led to the meeting
let maxEmailSeq = 0;
for (const emailEvent of emailHistory) {
  if (emailEvent.email_seq_number) {
    const seqNum = parseInt(emailEvent.email_seq_number, 10);
    if (!isNaN(seqNum) && seqNum > maxEmailSeq) {
      maxEmailSeq = seqNum;
    }
  }
}
```

#### Stage Processing
- Processes `stageHistory` array for each meeting
- Looks for stages: 'slots_sent' and 'booked'
- Uses `changedAt` timestamp to determine date
- Attributes slots to email sequence based on maxEmailSeq

### Data Flow
```
CRM API (/meetings) 
  → Extract meeting details
  → Process emailHistory (get fromEmailId, max sequence)
  → Process stageHistory (count slots/bookings)
  → Aggregate by date|client|funnel|fromEmailId
  → Batch insert
  → MeetingsFromReplit
```

## Attribution Logic Deep Dive

### How Attribution Works

1. **Email Sequence Determination**:
   - Looks at ALL emails in `emailHistory` array
   - Finds the MAXIMUM `email_seq_number` 
   - This determines which email "gets credit" for the meeting
   - If max = 1 → attributed to Email 1
   - If max = 2 → attributed to Email 2
   - If max = 3 → attributed to Email 3
   - If max = 0 or no valid number → attributed to "unknown"

2. **From Email Extraction**:
   - Filters emailHistory for type='SENT' only
   - Sorts by timestamp to find EARLIEST sent email
   - Takes the 'from' field as the sender (fromEmailId)
   - This identifies which inbox/sender was used

3. **Aggregation Key**:
   - Creates unique key: `${date}|${clientId}|${funnel}|${fromEmailId}`
   - All meetings with same key are grouped together
   - Counts are accumulated for slots_sent and booked

### Attribution Distribution (from actual data)
- **After Email 1**: 51.7% of meetings
- **After Email 2**: 21.1% of meetings  
- **After Email 3**: 9.2% of meetings
- **No Email History**: 18.0% of meetings
- **Unknown**: 0% (but would increase if email_seq_number was missing)

### Stage Processing Logic

For each meeting's `stageHistory`:
1. Iterates through all stage changes
2. For 'slots_sent' stage:
   - Increments counter based on max email sequence
   - Uses date from `changedAt` timestamp
3. For 'booked' stage:
   - Increments booked counter
   - Uses same aggregation key

### Why This Matters for Our New System

1. **Attribution is Working**: 82% of meetings have email history and can be attributed
2. **Sequence Distribution**: Most meetings (51.7%) happen after first email
3. **FromEmailId Available**: Can identify which inbox sent the emails
4. **Missing SP Connection**: Even though meetings have SP data, it's not in the aggregated table

## Important Notes

1. **Attribution Logic**: Uses MAX email sequence, not the email that actually triggered the meeting

2. **SP Data Lost**: The meetings data includes `jeffSearchPriority` field but it's NOT being stored in MeetingsFromReplit table

3. **Pre-Aggregation Problems**: 
   - Data is aggregated at date|client|funnel|fromEmailId level
   - Loses individual meeting details
   - Can't join back to original meetings

4. **Data Loss**: Original fields not stored:
   - jeffSearchPriority (SP data) - **Critical for our 5-dimensional analysis**
   - contactEmail (prospect email)
   - leadId (for joining with other data)
   - campaignId (for campaign-level analysis)
   - amazonSellerId (94.7% of meetings have this)

5. **Funnel Extraction**: Uses `meeting.campaignCategory?.code` which gives codes like "6A", "7B" etc.