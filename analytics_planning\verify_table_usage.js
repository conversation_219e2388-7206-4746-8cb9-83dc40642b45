/**
 * <PERSON><PERSON><PERSON> to verify which tables are actually used by dashboard queries
 */

const fs = require('fs');
const path = require('path');

// Load the dashboard queries analysis
const analysisFile = path.join(__dirname, '../analytics/context/dashboard_queries_analysis.json');
const data = JSON.parse(fs.readFileSync(analysisFile, 'utf8'));

// Tables we're checking for
const tablesToCheck = [
  'TagAnalytics',
  'ProviderAnalytics', 
  'FunnelClientAnalytics',
  'InboxesFromReplit',
  'InboxTagsFromReplit',
  'MeetingsFromReplit',
  'mv_email_with_thread_start'
];

// Table ID mappings (from the analysis)
const tableIdMappings = {
  '99': 'FunnelClientAnalytics',
  '94': 'MeetingsFromReplit'
};

console.log('VERIFYING TABLE USAGE IN DASHBOARD QUERIES');
console.log('='.repeat(60));

// Track usage
const tableUsage = {};
tablesToCheck.forEach(table => {
  tableUsage[table] = {
    queries: [],
    count: 0
  };
});

// Check each category
Object.entries(data.byCategory).forEach(([category, queries]) => {
  queries.forEach(query => {
    // Check data sources
    if (query.dataSources) {
      // Check explicit table names
      if (query.dataSources.tables) {
        query.dataSources.tables.forEach(table => {
          // Check if it's a Table ID
          if (table.startsWith('Table ID ')) {
            const tableId = table.replace('Table ID ', '');
            const actualTable = tableIdMappings[tableId];
            if (actualTable && tableUsage[actualTable]) {
              tableUsage[actualTable].queries.push({
                id: query.id,
                name: query.name,
                category
              });
              tableUsage[actualTable].count++;
            }
          } 
          // Direct table reference
          else if (tableUsage[table]) {
            tableUsage[table].queries.push({
              id: query.id,
              name: query.name,
              category
            });
            tableUsage[table].count++;
          }
        });
      }
    }
    
    // Also check SQL content if available
    if (query.sql) {
      tablesToCheck.forEach(table => {
        // Check for various forms of the table name
        const patterns = [
          `"${table}"`,
          `'${table}'`,
          `\`${table}\``,
          `public2.${table}`,
          `"public2"."${table}"`,
          ` ${table} `,
          ` ${table}`,
        ];
        
        patterns.forEach(pattern => {
          if (query.sql.includes(pattern)) {
            if (!tableUsage[table].queries.find(q => q.id === query.id)) {
              tableUsage[table].queries.push({
                id: query.id,
                name: query.name,
                category,
                foundIn: 'SQL'
              });
              tableUsage[table].count++;
            }
          }
        });
      });
    }
  });
});

// Report results
console.log('\nTABLE USAGE SUMMARY:');
console.log('-'.repeat(60));

tablesToCheck.forEach(table => {
  const usage = tableUsage[table];
  const status = usage.count > 0 ? '✅ USED' : '❌ NOT USED';
  console.log(`\n${table}: ${status}`);
  console.log(`  Used by ${usage.count} queries`);
  
  if (usage.count > 0) {
    console.log('  Queries:');
    usage.queries.forEach(q => {
      console.log(`    - Query ${q.id}: ${q.name} (${q.category})`);
    });
  }
});

// Summary statistics
const usedTables = tablesToCheck.filter(t => tableUsage[t].count > 0);
const unusedTables = tablesToCheck.filter(t => tableUsage[t].count === 0);

console.log('\n' + '='.repeat(60));
console.log('SUMMARY:');
console.log(`  Total tables checked: ${tablesToCheck.length}`);
console.log(`  Used tables: ${usedTables.length}`);
console.log(`  Unused tables: ${unusedTables.length}`);
console.log('\nUsed tables:', usedTables.join(', '));
console.log('Unused tables:', unusedTables.join(', '));

// Export for further analysis
const report = {
  timestamp: new Date().toISOString(),
  tableUsage,
  summary: {
    totalTables: tablesToCheck.length,
    usedTables: usedTables.length,
    unusedTables: unusedTables.length,
    usedTablesList: usedTables,
    unusedTablesList: unusedTables
  }
};

fs.writeFileSync(
  path.join(__dirname, 'table_usage_verification.json'),
  JSON.stringify(report, null, 2)
);

console.log('\nDetailed report saved to table_usage_verification.json');