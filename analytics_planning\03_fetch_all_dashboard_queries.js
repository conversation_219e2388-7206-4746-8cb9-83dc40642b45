/**
 * Step 3: Fetch ALL queries from Old Jeff Dashboard
 * Creates complete mapping of queries, their SQL, and table dependencies
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

const METABASE_URL = 'metabase.equalcollective.com';
const API_KEY = 'mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00=';
const DASHBOARD_ID = 2; // Old Jeff Dashboard

// Known table ID mappings
const TABLE_ID_MAPPINGS = {
  99: 'FunnelClientAnalytics',
  94: 'MeetingsFromReplit',
  // Will discover more as we go
};

function makeRequest(path, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: METABASE_URL,
      path: path,
      method: method,
      headers: {
        'X-API-KEY': API_KEY,
        'Content-Type': 'application/json'
      }
    };
    
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          console.error(`Error parsing response from ${path}:`, e.message);
          resolve(null);
        }
      });
    });
    
    req.on('error', reject);
    req.end();
  });
}

async function fetchDashboard() {
  console.log('📊 Fetching Old Jeff Dashboard...\n');
  const dashboard = await makeRequest(`/api/dashboard/${DASHBOARD_ID}`);
  
  if (!dashboard) {
    throw new Error('Failed to fetch dashboard');
  }
  
  console.log(`Dashboard: ${dashboard.name}`);
  console.log(`Cards on dashboard: ${dashboard.dashcards?.length || 0}`);
  console.log(`Parameters (filters): ${dashboard.parameters?.length || 0}\n`);
  
  return dashboard;
}

async function fetchQueryDetails(cardId) {
  const card = await makeRequest(`/api/card/${cardId}`);
  
  if (!card) {
    return null;
  }
  
  const details = {
    id: cardId,
    name: card.name,
    display: card.display,
    query_type: card.query_type,
    collection: card.collection?.name || 'Unknown',
    tables: [],
    sql: null,
    source_query: null,
    aggregations: [],
    filters: [],
    breakouts: []
  };
  
  // Extract query information based on type
  if (card.dataset_query) {
    const dq = card.dataset_query;
    
    if (dq.type === 'native') {
      // Native SQL query
      details.sql = dq.native?.query || '';
      details.tables = extractTablesFromSQL(details.sql);
      
    } else if (dq.type === 'query') {
      // GUI query
      const query = dq.query;
      
      // Source table
      if (query['source-table']) {
        const tableId = query['source-table'];
        if (typeof tableId === 'string' && tableId.startsWith('card__')) {
          // It's based on another query
          details.source_query = parseInt(tableId.replace('card__', ''));
        } else {
          // It's a table ID
          details.tables.push({
            id: tableId,
            name: TABLE_ID_MAPPINGS[tableId] || `Table_${tableId}`
          });
        }
      }
      
      // Source query (if query is based on another query)
      if (query['source-query']) {
        // Handle case where source-query might be an object
        if (typeof query['source-query'] === 'object') {
          // Try to extract the query ID from the object
          if (query['source-query']['source-table']) {
            const sourceTable = query['source-query']['source-table'];
            if (typeof sourceTable === 'string' && sourceTable.startsWith('card__')) {
              details.source_query = parseInt(sourceTable.replace('card__', ''));
            }
          }
        } else {
          details.source_query = query['source-query'];
        }
      }
      
      // Aggregations
      if (query.aggregation) {
        details.aggregations = query.aggregation;
      }
      
      // Filters
      if (query.filter) {
        details.filters = query.filter;
      }
      
      // Breakouts (GROUP BY)
      if (query.breakout) {
        details.breakouts = query.breakout;
      }
    }
  }
  
  return details;
}

function extractTablesFromSQL(sql) {
  const tables = [];
  const patterns = [
    /FROM\s+"?public2"?\."?(\w+)"?/gi,
    /FROM\s+"?(\w+)"?/gi,
    /JOIN\s+"?public2"?\."?(\w+)"?/gi,
    /JOIN\s+"?(\w+)"?/gi,
    /"public2"\."(\w+)"/gi
  ];
  
  const foundTables = new Set();
  
  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(sql)) !== null) {
      foundTables.add(match[1]);
    }
  });
  
  foundTables.forEach(table => {
    tables.push({
      name: table,
      schema: sql.includes(`"public2"."${table}"`) ? 'public2' : 'public'
    });
  });
  
  return tables;
}

async function fetchDashboardFilters(dashboard) {
  const filters = [];
  
  if (dashboard.parameters) {
    for (const param of dashboard.parameters) {
      filters.push({
        id: param.id,
        name: param.name,
        slug: param.slug,
        type: param.type,
        default: param.default,
        // Which cards this filter applies to
        mappings: param.mappings?.map(m => ({
          card_id: m.card_id,
          target: m.target
        })) || []
      });
    }
  }
  
  return filters;
}

async function main() {
  console.log('=' .repeat(60));
  console.log('STEP 3: COMPLETE DASHBOARD ANALYSIS');
  console.log('=' .repeat(60) + '\n');
  
  try {
    // 1. Fetch dashboard
    const dashboard = await fetchDashboard();
    
    // 2. Fetch all filters
    const filters = await fetchDashboardFilters(dashboard);
    console.log('📋 Dashboard Filters:');
    filters.forEach(filter => {
      console.log(`  - ${filter.name} (${filter.type}): Applies to ${filter.mappings.length} cards`);
    });
    console.log();
    
    // 3. Fetch all queries
    const queries = [];
    const queryMap = new Map();
    
    console.log('📊 Fetching all queries...\n');
    
    // Use dashcards instead of ordered_cards
    for (const dashcard of dashboard.dashcards || []) {
      const cardId = dashcard.card_id || dashcard.card?.id;
      if (!cardId) continue;
      
      console.log(`Fetching Query ${cardId}...`);
      const details = await fetchQueryDetails(cardId);
      
      if (details) {
        queries.push(details);
        queryMap.set(cardId, details);
        console.log(`  ✅ ${details.name}`);
        
        if (details.tables.length > 0) {
          console.log(`     Tables: ${details.tables.map(t => t.name || t.id).join(', ')}`);
        }
        if (details.source_query) {
          console.log(`     Based on: Query ${details.source_query}`);
        }
      }
    }
    
    // 4. Resolve query dependencies
    console.log('\n📈 Resolving query dependencies...');
    for (const query of queries) {
      if (query.source_query && typeof query.source_query === 'number') {
        // This query depends on another query
        if (!queryMap.has(query.source_query)) {
          // Fetch the dependency
          console.log(`  Fetching dependency: Query ${query.source_query}`);
          const dep = await fetchQueryDetails(query.source_query);
          if (dep) {
            queryMap.set(query.source_query, dep);
          }
        }
      }
    }
    
    // 5. Create complete mapping
    const mapping = {
      dashboard: {
        id: dashboard.id,
        name: dashboard.name,
        collection: dashboard.collection?.name
      },
      filters: filters,
      queries: Array.from(queryMap.values()),
      summary: {
        total_queries: queryMap.size,
        native_sql_queries: 0,
        gui_queries: 0,
        tables_used: new Set(),
        queries_with_dependencies: []
      }
    };
    
    // Calculate summary
    mapping.queries.forEach(q => {
      if (q.sql) {
        mapping.summary.native_sql_queries++;
      } else {
        mapping.summary.gui_queries++;
      }
      
      q.tables.forEach(t => {
        mapping.summary.tables_used.add(t.name || `Table_${t.id}`);
      });
      
      if (q.source_query) {
        mapping.summary.queries_with_dependencies.push({
          query: q.id,
          depends_on: q.source_query
        });
      }
    });
    
    mapping.summary.tables_used = Array.from(mapping.summary.tables_used);
    
    // 6. Save results
    const outputPath = path.join(__dirname, '03_dashboard_complete_mapping.json');
    fs.writeFileSync(outputPath, JSON.stringify(mapping, null, 2));
    
    // 7. Generate summary report
    console.log('\n' + '=' .repeat(60));
    console.log('📊 DASHBOARD ANALYSIS SUMMARY');
    console.log('=' .repeat(60));
    console.log(`\nDashboard: ${mapping.dashboard.name}`);
    console.log(`Total Queries: ${mapping.summary.total_queries}`);
    console.log(`  - Native SQL: ${mapping.summary.native_sql_queries}`);
    console.log(`  - GUI Queries: ${mapping.summary.gui_queries}`);
    console.log(`\nFilters: ${mapping.filters.length}`);
    mapping.filters.forEach(f => {
      console.log(`  - ${f.name} (${f.type})`);
    });
    console.log(`\nTables Used: ${mapping.summary.tables_used.length}`);
    mapping.summary.tables_used.forEach(t => {
      console.log(`  - ${t}`);
    });
    console.log(`\nQuery Dependencies: ${mapping.summary.queries_with_dependencies.length}`);
    mapping.summary.queries_with_dependencies.forEach(d => {
      console.log(`  - Query ${d.query} → Query ${d.depends_on}`);
    });
    
    console.log('\n✅ Complete mapping saved to: 03_dashboard_complete_mapping.json');
    
    // 8. Check for ProviderAnalytics specifically
    console.log('\n🔍 Checking for ProviderAnalytics usage...');
    let providerAnalyticsUsed = false;
    mapping.queries.forEach(q => {
      if (q.sql && q.sql.toLowerCase().includes('provideranalytics')) {
        console.log(`  ✅ Query ${q.id} (${q.name}) uses ProviderAnalytics in SQL`);
        providerAnalyticsUsed = true;
      }
      q.tables.forEach(t => {
        if (t.name?.toLowerCase() === 'provideranalytics') {
          console.log(`  ✅ Query ${q.id} (${q.name}) references ProviderAnalytics table`);
          providerAnalyticsUsed = true;
        }
      });
    });
    
    if (!providerAnalyticsUsed) {
      console.log('  ❌ ProviderAnalytics not found in any queries');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

main();