/**
 * Find the Old Jeff Dashboard ID
 */

const https = require('https');

const METABASE_URL = 'metabase.equalcollective.com';
const API_KEY = 'mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00=';

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: METABASE_URL,
      path: path,
      method: 'GET',
      headers: {
        'X-API-KEY': API_KEY
      }
    };
    
    https.get(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          reject(e);
        }
      });
    }).on('error', reject);
  });
}

async function findDashboards() {
  console.log('Searching for dashboards...\n');
  
  try {
    // Try to list all dashboards
    const dashboards = await makeRequest('/api/dashboard');
    
    console.log(`Found ${dashboards.length} dashboards:\n`);
    
    // Filter for Jeff or Analytics dashboards
    dashboards.forEach(dash => {
      const name = dash.name.toLowerCase();
      if (name.includes('jeff') || name.includes('analytics') || name.includes('old')) {
        console.log(`📊 ID: ${dash.id} - Name: "${dash.name}"`);
        console.log(`   Collection: ${dash.collection?.name || 'Root'}`);
        console.log(`   Created: ${dash.created_at}`);
        console.log();
      }
    });
    
    // Also show all dashboards for reference
    console.log('\nAll Dashboards:');
    dashboards.forEach(dash => {
      console.log(`  ${dash.id}: ${dash.name}`);
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

findDashboards();