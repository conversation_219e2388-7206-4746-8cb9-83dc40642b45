/**
 * Complete verification - checks both structure AND data
 * Ensures new MV has all columns AND all data from original
 */

require("dotenv").config({ path: "../.env" });
const prisma = require("../database/prisma/getPrismaClient");

async function verifyStructure() {
  console.log('1️⃣ STRUCTURE VERIFICATION\n');
  
  // Get columns from both MVs
  const [originalColumns, newColumns] = await Promise.all([
    prisma.$queryRawUnsafe(`
      SELECT column_name, ordinal_position, data_type
      FROM information_schema.columns
      WHERE table_name = 'mv_email_with_thread_start'
      ORDER BY ordinal_position
    `),
    prisma.$queryRawUnsafe(`
      SELECT column_name, ordinal_position, data_type
      FROM information_schema.columns
      WHERE table_name = 'mv_email_with_thread_start_v2'
      ORDER BY ordinal_position
    `)
  ]);
  
  console.log(`Original MV: ${originalColumns.length} columns`);
  console.log(`New MV (v2): ${newColumns.length} columns\n`);
  
  // Check all original columns exist
  const newColumnNames = newColumns.map(c => c.column_name);
  const missingColumns = originalColumns.filter(c => !newColumnNames.includes(c.column_name));
  
  if (missingColumns.length === 0) {
    console.log('✅ All original columns exist in new MV');
  } else {
    console.log('❌ Missing columns:', missingColumns.map(c => c.column_name).join(', '));
    return false;
  }
  
  // Check order
  let orderPreserved = true;
  for (let i = 0; i < originalColumns.length; i++) {
    if (originalColumns[i].column_name !== newColumns[i].column_name) {
      orderPreserved = false;
      break;
    }
  }
  
  console.log(orderPreserved ? '✅ Column order preserved' : '⚠️  Column order changed');
  
  // Show new columns
  const originalColumnNames = originalColumns.map(c => c.column_name);
  const addedColumns = newColumns.filter(c => !originalColumnNames.includes(c.column_name));
  console.log(`✅ Added ${addedColumns.length} new columns: ${addedColumns.map(c => c.column_name).join(', ')}\n`);
  
  return true;
}

async function verifyData() {
  console.log('2️⃣ DATA VERIFICATION\n');
  
  // Compare record counts
  const [originalCount, newCount] = await Promise.all([
    prisma.$queryRawUnsafe('SELECT COUNT(*) as count FROM mv_email_with_thread_start'),
    prisma.$queryRawUnsafe('SELECT COUNT(*) as count FROM mv_email_with_thread_start_v2')
  ]);
  
  const origTotal = parseInt(originalCount[0].count);
  const newTotal = parseInt(newCount[0].count);
  
  console.log(`Original MV: ${origTotal.toLocaleString()} records`);
  console.log(`New MV (v2): ${newTotal.toLocaleString()} records`);
  
  if (origTotal === newTotal) {
    console.log('✅ Record counts match\n');
  } else {
    const diff = newTotal - origTotal;
    const sign = diff > 0 ? '+' : '';
    console.log(`⚠️  Record count difference: ${sign}${diff.toLocaleString()}\n`);
  }
  
  // Compare data by sampling
  console.log('3️⃣ DATA SAMPLING VERIFICATION\n');
  
  // Check if specific records match
  const sampleIds = await prisma.$queryRawUnsafe(`
    SELECT id FROM mv_email_with_thread_start 
    ORDER BY id 
    LIMIT 100
  `);
  
  let matchCount = 0;
  let mismatchCount = 0;
  
  for (const row of sampleIds) {
    const [origRecord] = await prisma.$queryRawUnsafe(`
      SELECT * FROM mv_email_with_thread_start WHERE id = $1
    `, row.id);
    
    const [newRecord] = await prisma.$queryRawUnsafe(`
      SELECT * FROM mv_email_with_thread_start_v2 WHERE id = $1
    `, row.id);
    
    if (!newRecord) {
      mismatchCount++;
      console.log(`❌ Record ${row.id} missing in new MV`);
    } else {
      // Check key fields match
      const fieldsToCheck = ['leadId', 'type', 'toEmailID', 'fromEmailID', 'campaingId'];
      let recordMatches = true;
      
      for (const field of fieldsToCheck) {
        if (origRecord[field] !== newRecord[field]) {
          recordMatches = false;
          console.log(`❌ Record ${row.id}: ${field} mismatch`);
          break;
        }
      }
      
      if (recordMatches) matchCount++;
      else mismatchCount++;
    }
  }
  
  console.log(`\nSample check (100 records): ${matchCount} match, ${mismatchCount} mismatch`);
  
  // Check data distribution
  console.log('\n4️⃣ DATA DISTRIBUTION CHECK\n');
  
  const checks = [
    { name: 'Email types', query: 'SELECT type, COUNT(*) as count FROM {table} GROUP BY type ORDER BY type' },
    { name: 'Campaign distribution', query: 'SELECT COUNT(DISTINCT "campaingId") as campaigns FROM {table}' },
    { name: 'Date range', query: 'SELECT MIN("time") as min_date, MAX("time") as max_date FROM {table}' }
  ];
  
  for (const check of checks) {
    console.log(`Checking ${check.name}:`);
    
    const [origResult] = await prisma.$queryRawUnsafe(
      check.query.replace('{table}', 'mv_email_with_thread_start')
    );
    const [newResult] = await prisma.$queryRawUnsafe(
      check.query.replace('{table}', 'mv_email_with_thread_start_v2')
    );
    
    console.log(`  Original: ${JSON.stringify(origResult)}`);
    console.log(`  New:      ${JSON.stringify(newResult)}`);
    console.log('');
  }
  
  return matchCount > mismatchCount;
}

async function verifySPData() {
  console.log('5️⃣ SP DATA VERIFICATION\n');
  
  // Check SP distribution
  const spDist = await prisma.$queryRawUnsafe(`
    SELECT 
      sp,
      COUNT(*) as count,
      ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
    FROM mv_email_with_thread_start_v2
    GROUP BY sp
    ORDER BY sp
  `);
  
  console.log('SP Distribution in new MV:');
  console.log('SP     | Count      | Percentage');
  console.log('-------|------------|----------');
  
  let totalWithSP = 0;
  let totalWithoutSP = 0;
  
  spDist.forEach(row => {
    console.log(
      `${row.sp.padEnd(6)} | ${row.count.toString().padStart(10)} | ${row.percentage}%`
    );
    
    if (row.sp === 'UNK') {
      totalWithoutSP = parseInt(row.count);
    } else {
      totalWithSP += parseInt(row.count);
    }
  });
  
  const spCoverage = (totalWithSP / (totalWithSP + totalWithoutSP) * 100).toFixed(1);
  console.log(`\n✅ SP Coverage: ${spCoverage}% have SP data`);
  console.log(`   ${totalWithSP.toLocaleString()} with SP`);
  console.log(`   ${totalWithoutSP.toLocaleString()} without SP (UNK)`);
  
  // Verify SP join worked
  const sampleWithSP = await prisma.$queryRawUnsafe(`
    SELECT 
      "toEmailID",
      sp,
      email_provider,
      domain_type
    FROM mv_email_with_thread_start_v2
    WHERE sp != 'UNK'
    LIMIT 5
  `);
  
  console.log('\n📌 Sample records with SP:');
  sampleWithSP.forEach(row => {
    console.log(`  ${row.toEmailID} → ${row.sp} | ${row.email_provider} | ${row.domain_type}`);
  });
}

async function main() {
  console.log('🚀 COMPLETE MV VERIFICATION');
  console.log('=' .repeat(60) + '\n');
  
  try {
    const structureOk = await verifyStructure();
    const dataOk = await verifyData();
    await verifySPData();
    
    console.log('\n' + '=' .repeat(60));
    console.log('📋 FINAL VERIFICATION RESULTS:\n');
    
    if (structureOk && dataOk) {
      console.log('✅ NEW MV IS FULLY COMPATIBLE AND READY FOR PRODUCTION');
      console.log('   - All original columns present');
      console.log('   - All original data present');
      console.log('   - SP data successfully added');
      console.log('\n🎯 Safe to switch to mv_email_with_thread_start_v2');
    } else {
      console.log('⚠️  VERIFICATION FOUND ISSUES');
      console.log('   Review the output above for details');
      console.log('\n❌ Do not switch to production until issues are resolved');
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();