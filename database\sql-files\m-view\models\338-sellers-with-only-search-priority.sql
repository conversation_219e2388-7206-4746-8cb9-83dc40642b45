DROP MATERIALIZED VIEW IF EXISTS "public"."SellersWithOnlySearchPriority";
CREATE MATERIALIZED VIEW "public"."SellersWithOnlySearchPriority" AS
WITH company_data AS (
SELECT
  c.id,
  c.seller_group_id,
  c.amazon_seller_id,
  c.marketplace,
  c.business_name,
  c.name,
  c.domain,
  c.street,
  c.city,
  c.adr_state,
  c.adr_country,
  c.adr_zip_code,
  c.website_status,
  CASE
    WHEN c.marketplace = 'US' THEN concat(
      'https://www.amazon.com/sp?seller=',
      c.amazon_seller_id
    )
    WHEN c.marketplace = 'UK' THEN concat(
      'https://www.amazon.co.uk/sp?seller=',
      c.amazon_seller_id
    )
    WHEN c.marketplace = 'DE' THEN concat(
      'https://www.amazon.de/sp?seller=',
      c.amazon_seller_id
    )
    WHEN c.marketplace = 'JP' THEN concat(
      'https://www.amazon.co.jp/sp?seller=',
      c.amazon_seller_id
    )
    WHEN c.marketplace = 'CA' THEN concat(
      'https://www.amazon.ca/sp?seller=',
      c.amazon_seller_id
    )
    WHEN c.marketplace = 'FR' THEN concat(
      'https://www.amazon.fr/sp?seller=',
      c.amazon_seller_id
    )
    WHEN c.marketplace = 'IT' THEN concat(
      'https://www.amazon.it/sp?seller=',
      c.amazon_seller_id
    )
    WHEN c.marketplace = 'ES' THEN concat(
      'https://www.amazon.es/sp?seller=',
      c.amazon_seller_id
    )
    WHEN c.marketplace = 'IN' THEN concat(
      'https://www.amazon.in/sp?seller=',
      c.amazon_seller_id
    )
    -- Add more marketplaces as needed
    ELSE 'NOT AVAILABLE'
  END AS seller_url,
  c.lookup_sources,
  c.lookup_source,
  CASE
    WHEN c.adr_country = 'CN' THEN 'SP9'
    WHEN (
      c.estimate_sales IS NULL
      OR c.estimate_sales = 0
    )
    AND COALESCE(c.adr_country, '') IN (
      'US',
      'GB',
      'AU',
      'IL',
      'DE',
      'ES',
      'FR',
      'NL',
      'NZ',
      'IE',
      'CH',
      'EE',
      'SE',
      'BE',
      'CA'
    ) THEN 'SP4'
    WHEN (
      c.estimate_sales IS NULL
      OR c.estimate_sales = 0
    )
    AND COALESCE(c.adr_country, '') NOT IN (
      'US',
      'GB',
      'AU',
      'IL',
      'DE',
      'ES',
      'FR',
      'NL',
      'NZ',
      'IE',
      'CH',
      'EE',
      'SE',
      'BE',
      'CA'
    ) THEN 'SP8'
    WHEN c.estimate_sales > 0
    AND c.estimate_sales <= 10000
    AND COALESCE(c.adr_country, '') IN (
      'US',
      'GB',
      'AU',
      'IL',
      'DE',
      'ES',
      'FR',
      'NL',
      'NZ',
      'IE',
      'CH',
      'EE',
      'SE',
      'BE',
      'CA'
    ) THEN 'SP3B'
    WHEN c.estimate_sales > 10000
    AND c.estimate_sales < 20000
    AND COALESCE(c.adr_country, '') IN (
      'US',
      'GB',
      'AU',
      'IL',
      'DE',
      'ES',
      'FR',
      'NL',
      'NZ',
      'IE',
      'CH',
      'EE',
      'SE',
      'BE',
      'CA'
    ) THEN 'SP3A'
    WHEN c.estimate_sales > 0
    AND c.estimate_sales < 20000
    AND COALESCE(c.adr_country, '') NOT IN (
      'US',
      'GB',
      'AU',
      'IL',
      'DE',
      'ES',
      'FR',
      'NL',
      'NZ',
      'IE',
      'CH',
      'EE',
      'SE',
      'BE',
      'CA'
    ) THEN 'SP7'
    WHEN c.estimate_sales > 20000
    AND c.estimate_sales < 50000
    AND COALESCE(c.adr_country, '') IN (
      'US',
      'GB',
      'AU',
      'IL',
      'DE',
      'ES',
      'FR',
      'NL',
      'NZ',
      'IE',
      'CH',
      'EE',
      'SE',
      'BE',
      'CA'
    ) THEN 'SP2'
    WHEN c.estimate_sales > 20000
    AND c.estimate_sales < 50000
    AND COALESCE(c.adr_country, '') NOT IN (
      'US',
      'GB',
      'AU',
      'IL',
      'DE',
      'ES',
      'FR',
      'NL',
      'NZ',
      'IE',
      'CH',
      'EE',
      'SE',
      'BE',
      'CA'
    ) THEN 'SP6'
    WHEN c.estimate_sales > 50000
    AND COALESCE(c.adr_country, '') IN (
      'US',
      'GB',
      'AU',
      'IL',
      'DE',
      'ES',
      'FR',
      'NL',
      'NZ',
      'IE',
      'CH',
      'EE',
      'SE',
      'BE',
      'CA'
    ) THEN 'SP1'
    WHEN c.estimate_sales > 50000
    AND COALESCE(c.adr_country, '') NOT IN (
      'US',
      'GB',
      'AU',
      'IL',
      'DE',
      'ES',
      'FR',
      'NL',
      'NZ',
      'IE',
      'CH',
      'EE',
      'SE',
      'BE',
      'CA'
    ) THEN 'SP5'
    ELSE NULL
  END AS jeff_search_priority
FROM
  "public"."AmazonSeller" c
  where c.adr_country <> 'CN'
)
SELECT 
  *,
  MIN(jeff_search_priority) OVER (PARTITION BY seller_group_id) AS dominant_search_priority
FROM company_data;

-- Indexes for the materialized view
-- Run these AFTER creating the materialized view
DROP INDEX IF EXISTS idx_sellers_search_priority;
DROP INDEX IF EXISTS idx_sellers_marketplace;
DROP INDEX IF EXISTS idx_sellers_marketplace_priority;
DROP INDEX IF EXISTS idx_sellers_amazon_seller_id;
DROP INDEX IF EXISTS idx_sellers_seller_group_id;
DROP INDEX IF EXISTS idx_sellers_lookup_sources;
DROP INDEX IF EXISTS idx_sellers_seller_group_search_priority;

-- Primary index on search priority (most important for filtering)
CREATE INDEX IF NOT EXISTS idx_sellers_search_priority 
ON "public"."SellersWithOnlySearchPriority" (jeff_search_priority);

-- Index on marketplace for marketplace-specific queries
CREATE INDEX IF NOT EXISTS idx_sellers_marketplace 
ON "public"."SellersWithOnlySearchPriority" (marketplace);

-- Composite index for marketplace + priority queries
CREATE INDEX IF NOT EXISTS idx_sellers_marketplace_priority 
ON "public"."SellersWithOnlySearchPriority" (marketplace, jeff_search_priority);

-- Index on seller ID for lookups
CREATE INDEX IF NOT EXISTS idx_sellers_amazon_seller_id 
ON "public"."SellersWithOnlySearchPriority" (amazon_seller_id);

-- Index on seller group ID 
CREATE INDEX IF NOT EXISTS idx_sellers_seller_group_id 
ON "public"."SellersWithOnlySearchPriority" (seller_group_id);

-- GIN index for JSON lookup_sources field
CREATE INDEX IF NOT EXISTS idx_sellers_lookup_sources 
ON "public"."SellersWithOnlySearchPriority" USING GIN (lookup_sources);

-- Index on seller group search priority
CREATE INDEX IF NOT EXISTS idx_sellers_seller_group_search_priority 
ON "public"."SellersWithOnlySearchPriority" (dominant_search_priority);