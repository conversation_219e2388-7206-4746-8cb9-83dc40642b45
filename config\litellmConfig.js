/**
 * Centralized LiteLLM Configuration for SellerBot
 * 
 * This configuration ensures all AI services use LiteLLM as the base URL
 * with standardized tagging format: [sellerbot, service_name, function_name]
 */

require('dotenv').config();

const LITELLM_CONFIG = {
    // Core LiteLLM settings
    baseURL: process.env.LITELLM_PROXY_URL || 'https://ai.gateway.equalcollective.com',
    apiKey: process.env.LITELLM_API_KEY || process.env.OPENAI_API_KEY,
    
    // Default models in preference order
    defaultModels: {
        primary: 'gpt-4o',
        fallback: ['gpt-4', 'gpt-3.5-turbo', 'claude-3-sonnet-20240229'],
        gemini: {
            flash: 'gemini/gemini-2.5-flash',
            pro: 'gemini/gemini-2.5-pro'
        }
    },
    
    // Available models for SellerBot
    availableModels: [
        'gpt-4o',
        'gpt-4',
        'gpt-3.5-turbo',
        'claude-3-sonnet-20240229',
        'gemini/gemini-2.5-flash',
        'gemini/gemini-2.5-pro'
    ],
    
    // Default request parameters
    defaults: {
        temperature: 0.7,
        max_tokens: 1000,
        top_p: 1.0,
        frequency_penalty: 0,
        presence_penalty: 0,
        timeout: 30000, // 30 seconds
        retries: 3
    },
    
    // Service-specific configurations
    services: {
        scrapeGPT: {
            defaultModel: 'gpt-4o',
            temperature: 1.0,
            max_tokens: 256,
            useCase: 'content_analysis',
            operationType: 'scrape_analysis'
        },
        assistant: {
            defaultModel: 'gpt-4o',
            temperature: 0.7,
            max_tokens: 1000,
            useCase: 'structured_analysis',
            operationType: 'assistant_run'
        },
        leadGeneration: {
            defaultModel: 'gpt-4o',
            temperature: 0.5,
            max_tokens: 500,
            useCase: 'lead_analysis',
            operationType: 'lead_generation'
        },
        emailAnalysis: {
            defaultModel: 'gpt-4o',
            temperature: 0.3,
            max_tokens: 300,
            useCase: 'email_classification',
            operationType: 'email_analysis'
        },
        centralizedAI: {
            defaultModel: 'gpt-4o',
            temperature: 0.7,
            max_tokens: 1000,
            useCase: 'general_ai',
            operationType: 'chat_completion'
        },
        portkeyWrapper: {
            defaultModel: 'gpt-4o',
            temperature: 0.7,
            max_tokens: 1000,
            useCase: 'portkey_integration',
            operationType: 'chat_completion'
        }
    },
    
    // Tagging configuration
    tagging: {
        // Core tags format: [sellerbot, service_name, function_name]
        coreTagsFormat: ['sellerbot', '{service}', '{function}'],
        
        // Standard metadata tags
        standardTags: {
            environment: process.env.NODE_ENV || 'development',
            service: 'SellerBot',
            version: '1.0.0',
            platform: 'nodejs'
        },
        
        // Business context tags
        businessTags: [
            'useCase',
            'operationType',
            'feature',
            'priority',
            'batchId',
            'campaignId',
            'domain',
            'assistantId'
        ]
    },
    
    // Health check configuration
    healthCheck: {
        endpoint: '/health',
        timeout: 10000,
        retries: 2
    },
    
    // Logging configuration
    logging: {
        enabled: process.env.AI_METADATA_LOGGING === 'true',
        level: process.env.LOG_LEVEL || 'info',
        includeMetadata: true,
        includeUsage: true
    },
    
    // Error handling configuration
    errorHandling: {
        retryOnRateLimit: true,
        retryDelay: 1000, // 1 second
        maxRetryDelay: 10000, // 10 seconds
        exponentialBackoff: true,
        fallbackToSecondaryModel: true
    }
};

/**
 * Get configuration for a specific service
 */
function getServiceConfig(serviceName) {
    const serviceConfig = LITELLM_CONFIG.services[serviceName] || LITELLM_CONFIG.services.centralizedAI;
    
    return {
        ...LITELLM_CONFIG.defaults,
        ...serviceConfig,
        baseURL: `${LITELLM_CONFIG.baseURL}/v1`,
        apiKey: LITELLM_CONFIG.apiKey
    };
}

/**
 * Get OpenAI client configuration for LiteLLM
 */
function getOpenAIConfig(serviceName = 'centralizedAI') {
    return {
        apiKey: LITELLM_CONFIG.apiKey,
        baseURL: `${LITELLM_CONFIG.baseURL}/v1`,
        timeout: LITELLM_CONFIG.defaults.timeout,
        maxRetries: LITELLM_CONFIG.defaults.retries
    };
}

/**
 * Validate LiteLLM configuration
 */
function validateConfig() {
    const errors = [];
    
    if (!LITELLM_CONFIG.apiKey) {
        errors.push('LITELLM_API_KEY or OPENAI_API_KEY environment variable is required');
    }
    
    if (!LITELLM_CONFIG.baseURL) {
        errors.push('LITELLM_PROXY_URL environment variable is required');
    }
    
    if (!LITELLM_CONFIG.apiKey?.startsWith('sk-')) {
        errors.push('API key should start with "sk-"');
    }
    
    return {
        isValid: errors.length === 0,
        errors: errors,
        warnings: []
    };
}

/**
 * Get model preference order for fallback
 */
function getModelFallbackOrder(preferredModel = null) {
    const models = [
        preferredModel,
        LITELLM_CONFIG.defaultModels.primary,
        ...LITELLM_CONFIG.defaultModels.fallback
    ].filter(Boolean);
    
    // Remove duplicates while preserving order
    return [...new Set(models)];
}

/**
 * Check if a model is available
 */
function isModelAvailable(modelName) {
    return LITELLM_CONFIG.availableModels.includes(modelName);
}

/**
 * Get the best available model for a preference
 */
function getBestModel(preferredModel = null) {
    const fallbackOrder = getModelFallbackOrder(preferredModel);
    
    for (const model of fallbackOrder) {
        if (isModelAvailable(model)) {
            return model;
        }
    }
    
    // Return first available model as last resort
    return LITELLM_CONFIG.availableModels[0] || LITELLM_CONFIG.defaultModels.primary;
}

module.exports = {
    LITELLM_CONFIG,
    getServiceConfig,
    getOpenAIConfig,
    validateConfig,
    getModelFallbackOrder,
    isModelAvailable,
    getBestModel
};
