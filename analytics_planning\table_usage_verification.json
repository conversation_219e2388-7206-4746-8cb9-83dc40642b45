{"timestamp": "2025-09-15T22:06:44.397Z", "tableUsage": {"TagAnalytics": {"queries": [], "count": 0}, "ProviderAnalytics": {"queries": [], "count": 0}, "FunnelClientAnalytics": {"queries": [{"id": 325, "name": "eMail 1 Sent by Role Week on Week", "category": "<PERSON><PERSON> Analytics"}, {"id": 324, "name": "eMail 1, eMail 2, eMail 3 sent week on week", "category": "<PERSON><PERSON> Analytics"}, {"id": 294, "name": "Prospects Reached (by Week)", "category": "Prospect Analytics"}, {"id": 326, "name": "Prospects Reached by Client", "category": "Prospect Analytics"}, {"id": 297, "name": "Raw Numbers by Funnel", "category": "Other Analytics"}], "count": 5}, "InboxesFromReplit": {"queries": [], "count": 0}, "InboxTagsFromReplit": {"queries": [], "count": 0}, "MeetingsFromReplit": {"queries": [{"id": 337, "name": "Meetings Booked by Funnel", "category": "Meeting Analytics"}, {"id": 308, "name": "Meetings CRM Stats by Client", "category": "Meeting Analytics"}, {"id": 335, "name": "Slots Sent by Funnel", "category": "Slot Analytics"}], "count": 3}, "mv_email_with_thread_start": {"queries": [], "count": 0}}, "summary": {"totalTables": 7, "usedTables": 2, "unusedTables": 5, "usedTablesList": ["FunnelClientAnalytics", "MeetingsFromReplit"], "unusedTablesList": ["TagAnalytics", "ProviderAnalytics", "InboxesFromReplit", "InboxTagsFromReplit", "mv_email_with_thread_start"]}}