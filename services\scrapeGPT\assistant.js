const { OpenAI } = require("openai");
const fs = require("fs");
const { serviceGenerators } = require("../../utils/aiMetadataHelper");
require("dotenv").config();

// Function to create a thread run with the specified parameters
async function getAssistantResponse(assistantId, json_string, options = {}) {
  try {
    // Initialize OpenAI client with LiteLLM gateway
    const open_ai = new OpenAI({
      apiKey: process.env.LITELLM_API_KEY || process.env.OPENAI_API_KEY,
      baseURL: 'https://ai.gateway.equalcollective.com/v1',
      model: process.env.OPENAI_MODEL_ID
    });
    const assistant_id = assistantId;

    // Generate standardized tags for assistant run
    // Use custom service name if provided, otherwise use 'assistant'
    const serviceName = options.serviceName || 'assistant';
    const functionName = options.serviceName || 'getAssistantResponse';

    const tags = serviceGenerators.assistant.generateTags(functionName, {
      batchId: options.batchId,
      customTags: options.customTags || []
    });

    // Override the service name in tags if custom service name provided
    if (options.serviceName) {
      tags[1] = serviceName;
    }

    // Create thread and run with metadata
    let run = await open_ai.beta.threads.createAndRun({
      assistant_id: assistant_id,
      thread: {
        messages: [{ role: "user", content: json_string }],
      },
      metadata: {
        tags: tags
      }
    });

    // console.log("Thread run created:", run);
    while (["queued", "in_progress", "cancelling"].includes(run.status)) {
      console.log("Waiting for thread run to complete...", run.status);
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait for 1 second
      run = await open_ai.beta.threads.runs.retrieve(run.thread_id, run.id);
    }
    console.log("Thread run completed:", run.status);
    result = run.usage;
    result["gptDetails"] = run.model;
    result["prompt"] = JSON.stringify(run.instructions);
    result["metadata"] = {
      tags: tags,
      assistantId: assistantId,
      threadId: run.thread_id,
      runId: run.id
    };

    if (run.status === "completed") {
      const messages = await open_ai.beta.threads.messages.list(run.thread_id);
      // console.log("Messages:" + JSON.stringify(messages));
      for (const message of messages.data.reverse()) {
        // console.log(`${message.role} > ${message.content[0].text.value}`);
        if (message.role === "assistant") {
          result["message"] = message.content[0].text.value.replace(
            /"""/g,
            " ",
          );
          return result;
        }
      }
    } else {
      console.log("Thread run failed:", run.last_error?.message || "Unknown error");
    }
    return result;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    // if(error.response.data.error.code === 'insufficient_funds') {
    //   // sendErrorEmail("gptCreditError");
    //   console.log("Insufficient funds");
    // }
    // throw new Error("Error creating thread run: " + error);
  }
}

// Example usage
async function exampleUsage() {
  try {
    const json = fs.readFileSync("scrapeGPT/data.json", "utf8");
    const result = await getAssistantResponse("asst_example", JSON.stringify(json), {
      useCase: 'test',
      customTags: ['test:example']
    });
    console.log(result);
    console.log("Metadata Tags:", result.metadata.tags);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error in exampleUsage:", error);
  }
}

module.exports = {
  getAssistantResponse,
};
