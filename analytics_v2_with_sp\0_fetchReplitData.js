/**
 * Step 0: Fetch data from Replit and create analytics tables
 * This replicates what generateAnalytics.sh does
 * Run: node analytics_v2_with_sp/0_fetchReplitData.js
 */

require("dotenv").config({ path: "../.env" });
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
const prisma = require("../database/prisma/getPrismaClient");

async function runToolingScript(script, args = '') {
  console.log(`   Running: node tooling/${script} ${args}`);
  try {
    const { stdout, stderr } = await execPromise(
      `cd .. && node tooling/${script} ${args}`,
      { cwd: __dirname }
    );
    if (stdout) console.log(stdout);
    if (stderr && !stderr.includes('Warning')) console.error(stderr);
    return true;
  } catch (error) {
    console.error(`   ❌ Failed: ${error.message}`);
    return false;
  }
}

async function fetchAllReplitData() {
  console.log('📥 Fetching all data from Replit and creating analytics tables...\n');
  
  const steps = [
    { name: 'TagAnalytics', script: 'createMetabaseAnalyticsTables.js', args: 'tag' },
    { name: 'ProviderAnalytics', script: 'createMetabaseAnalyticsTables.js', args: 'providerAnalytics' },
    { name: 'FunnelClientAnalytics', script: 'createMetabaseAnalyticsTables.js', args: 'funnelClient' },
    { name: 'InboxesFromReplit', script: 'syncEmailTags.js', args: 'inboxes' },
    { name: 'MeetingsFromReplit', script: 'syncEmailTags.js', args: 'meetings' }
  ];
  
  let success = 0;
  let failed = 0;
  
  for (const [index, step] of steps.entries()) {
    console.log(`${index + 1}️⃣ Creating ${step.name}...`);
    const result = await runToolingScript(step.script, step.args);
    if (result) {
      console.log(`✅ ${step.name} created\n`);
      success++;
    } else {
      console.log(`❌ ${step.name} failed\n`);
      failed++;
    }
  }
  
  console.log(`\n📊 Summary: ${success} succeeded, ${failed} failed`);
  
  // Verify tables exist
  try {
    const tables = await prisma.$queryRawUnsafe(`
      SELECT tablename,
             (SELECT COUNT(*) FROM information_schema.tables 
              WHERE table_name = tablename) as exists
      FROM (VALUES 
        ('TagAnalytics'),
        ('ProviderAnalytics'),
        ('FunnelClientAnalytics'),
        ('InboxesFromReplit'),
        ('MeetingsFromReplit')
      ) AS t(tablename)
    `);
    
    console.log('\n📋 Table Status:');
    tables.forEach(table => {
      const status = table.exists > 0 ? '✅' : '❌';
      console.log(`   ${status} ${table.tablename}`);
    });
  } catch (error) {
    console.log('   Could not verify tables');
  }
  
  return success > 0;
}

async function main() {
  console.log('🚀 Step 0: Fetching Replit Data & Creating Analytics Tables');
  console.log('=' .repeat(60));
  
  try {
    const success = await fetchAllReplitData();
    
    console.log('\n' + '=' .repeat(60));
    if (success) {
      console.log('✅ Replit data fetch complete');
      console.log('   Next: Run 1_fetchSPData.js to add SP data');
    } else {
      console.log('⚠️  Some steps failed, but you can continue');
    }
  } catch (error) {
    console.error('Failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Only run if executed directly
if (require.main === module) {
  main();
}

module.exports = { fetchAllReplitData };