/**
 * Test script to compare new 5D analytics with existing system
 * Validates data accuracy and consistency
 */

const prisma = require("../../database/prisma/getPrismaClient");
const fs = require('fs');
const path = require('path');

/**
 * Compare metrics between old and new systems
 */
async function compareAnalytics() {
  console.log('🔍 ANALYTICS COMPARISON TEST\n');
  console.log('=' .repeat(60));
  console.log('Comparing new 5D analytics with existing FunnelClientAnalytics\n');
  
  try {
    // 1. Check if tables exist
    console.log('📋 Checking tables...');
    
    const tables = await prisma.$queryRaw`
      SELECT 
        table_schema,
        table_name,
        (SELECT COUNT(*) FROM information_schema.tables 
         WHERE table_schema = t.table_schema 
         AND table_name = t.table_name) as exists
      FROM (VALUES 
        ('public2', 'analytics_5_dimensions'),
        ('public2', 'FunnelClientAnalytics'),
        ('public2', 'MeetingsFromReplit')
      ) AS t(table_schema, table_name)
    `;
    
    console.log('Table Status:');
    tables.forEach(t => {
      const status = t.exists > 0 ? '✅' : '❌';
      console.log(`   ${status} ${t.table_schema}.${t.table_name}`);
    });
    
    // 2. Compare overall metrics
    console.log('\n📊 Overall Metrics Comparison:\n');
    
    // New system metrics
    const newMetrics = await prisma.$queryRaw`
      SELECT 
        COUNT(DISTINCT "client") as unique_clients,
        COUNT(DISTINCT "funnel") as unique_funnels,
        COUNT(DISTINCT "sp") as unique_sps,
        SUM("email_1_sent_count") as total_email_1,
        SUM("email_2_sent_count") as total_email_2,
        SUM("email_3_sent_count") as total_email_3,
        SUM("replies_after_email_1") as replies_1,
        SUM("replies_after_email_2") as replies_2,
        SUM("replies_after_email_3") as replies_3,
        SUM("meetings_booked") as meetings_booked
      FROM "public2"."analytics_5_dimensions"
      WHERE "week_start_date" >= CURRENT_DATE - INTERVAL '30 days'
    `;
    
    // Old system metrics (if table exists)
    let oldMetrics = null;
    try {
      oldMetrics = await prisma.$queryRaw`
        SELECT 
          COUNT(DISTINCT "client") as unique_clients,
          COUNT(DISTINCT "funnel") as unique_funnels,
          SUM("email_1_sent") as total_email_1,
          SUM("email_2_sent") as total_email_2,
          SUM("email_3_sent") as total_email_3,
          SUM("email_1_reply") as replies_1,
          SUM("email_2_reply") as replies_2,
          SUM("email_3_reply") as replies_3
        FROM "public2"."FunnelClientAnalytics"
        WHERE "weekStartDate" >= CURRENT_DATE - INTERVAL '30 days'
      `;
    } catch (e) {
      console.log('   ⚠️  FunnelClientAnalytics not available for comparison');
    }
    
    // Display comparison
    console.log('Metric                    | New System    | Old System    | Difference');
    console.log('-'.repeat(72));
    
    if (newMetrics[0] && oldMetrics && oldMetrics[0]) {
      const metrics = [
        ['Unique Clients', 'unique_clients'],
        ['Unique Funnels', 'unique_funnels'],
        ['Email 1 Sent', 'total_email_1'],
        ['Email 2 Sent', 'total_email_2'],
        ['Email 3 Sent', 'total_email_3'],
        ['Replies after Email 1', 'replies_1'],
        ['Replies after Email 2', 'replies_2'],
        ['Replies after Email 3', 'replies_3']
      ];
      
      metrics.forEach(([label, field]) => {
        const newVal = parseInt(newMetrics[0][field] || 0);
        const oldVal = parseInt(oldMetrics[0][field] || 0);
        const diff = newVal - oldVal;
        const diffStr = diff > 0 ? `+${diff}` : diff.toString();
        
        console.log(
          `${label.padEnd(25)} | ${newVal.toString().padStart(13)} | ${oldVal.toString().padStart(13)} | ${diffStr.padStart(10)}`
        );
      });
    } else if (newMetrics[0]) {
      console.log('New System Metrics:');
      Object.entries(newMetrics[0]).forEach(([key, value]) => {
        console.log(`   ${key}: ${value}`);
      });
    }
    
    // 3. Test dimension filtering
    console.log('\n🔍 Testing Dimension Filters:\n');
    
    const filterTests = [
      { filter: { sp: 'SP1' }, description: 'Filter by SP=SP1' },
      { filter: { email_provider: 'Maildoso' }, description: 'Filter by Provider=Maildoso' },
      { filter: { domain_type: 'standard' }, description: 'Filter by Domain=standard' },
      { filter: { client: 'AMZ Ads' }, description: 'Filter by Client=AMZ Ads' },
      { filter: { funnel: '5B' }, description: 'Filter by Funnel=5B' }
    ];
    
    for (const test of filterTests) {
      const whereClause = Object.entries(test.filter)
        .map(([key, value]) => `"${key}" = '${value}'`)
        .join(' AND ');
      
      const result = await prisma.$queryRawUnsafe(`
        SELECT 
          COUNT(*) as row_count,
          SUM("email_1_sent_count" + "email_2_sent_count" + "email_3_sent_count") as total_emails
        FROM "public2"."analytics_5_dimensions"
        WHERE ${whereClause}
      `);
      
      console.log(`${test.description}:`);
      console.log(`   Rows: ${result[0].row_count}, Total Emails: ${result[0].total_emails || 0}`);
    }
    
    // 4. Test combination filters
    console.log('\n🔗 Testing Combined Filters:\n');
    
    const comboResult = await prisma.$queryRaw`
      SELECT 
        "sp",
        SUM("email_1_sent_count") as email_1,
        SUM("replies_after_email_1") as replies_1
      FROM "public2"."analytics_5_dimensions"
      WHERE "client" = 'AMZ Ads' 
        AND "funnel" = '5B'
      GROUP BY "sp"
      ORDER BY "sp"
      LIMIT 5
    `;
    
    if (comboResult.length > 0) {
      console.log('SP breakdown for Client=AMZ Ads, Funnel=5B:');
      console.log('SP    | Email 1 Sent | Replies');
      console.log('-'.repeat(35));
      comboResult.forEach(row => {
        console.log(`${row.sp.padEnd(5)} | ${row.email_1.toString().padStart(12)} | ${row.replies_1.toString().padStart(7)}`);
      });
    } else {
      console.log('No data found for Client=AMZ Ads, Funnel=5B');
    }
    
    // 5. Performance test
    console.log('\n⚡ Performance Test:\n');
    
    const queries = [
      {
        name: 'Simple aggregation',
        query: `SELECT SUM("email_1_sent_count") FROM "public2"."analytics_5_dimensions"`
      },
      {
        name: 'Filtered aggregation',
        query: `SELECT SUM("email_1_sent_count") FROM "public2"."analytics_5_dimensions" WHERE "sp" = 'SP1'`
      },
      {
        name: 'Group by with multiple dimensions',
        query: `SELECT "client", "funnel", SUM("email_1_sent_count") 
                FROM "public2"."analytics_5_dimensions" 
                GROUP BY "client", "funnel" 
                LIMIT 10`
      }
    ];
    
    for (const test of queries) {
      const start = Date.now();
      await prisma.$queryRawUnsafe(test.query);
      const time = Date.now() - start;
      console.log(`${test.name}: ${time}ms`);
    }
    
    // 6. Data quality checks
    console.log('\n✅ Data Quality Checks:\n');
    
    // Check for nulls in dimensions
    const nullCheck = await prisma.$queryRaw`
      SELECT 
        SUM(CASE WHEN "client" = 'UNK' THEN 1 ELSE 0 END) as unk_clients,
        SUM(CASE WHEN "funnel" = 'UNK' THEN 1 ELSE 0 END) as unk_funnels,
        SUM(CASE WHEN "sp" = 'UNK' THEN 1 ELSE 0 END) as unk_sps,
        SUM(CASE WHEN "email_provider" = 'UNK' THEN 1 ELSE 0 END) as unk_providers,
        SUM(CASE WHEN "domain_type" = 'unknown' THEN 1 ELSE 0 END) as unk_domains,
        COUNT(*) as total_rows
      FROM "public2"."analytics_5_dimensions"
    `;
    
    const nc = nullCheck[0];
    console.log(`Total rows: ${nc.total_rows}`);
    console.log(`UNK values:`);
    console.log(`   Clients: ${nc.unk_clients} (${(nc.unk_clients/nc.total_rows*100).toFixed(1)}%)`);
    console.log(`   Funnels: ${nc.unk_funnels} (${(nc.unk_funnels/nc.total_rows*100).toFixed(1)}%)`);
    console.log(`   SPs: ${nc.unk_sps} (${(nc.unk_sps/nc.total_rows*100).toFixed(1)}%)`);
    console.log(`   Providers: ${nc.unk_providers} (${(nc.unk_providers/nc.total_rows*100).toFixed(1)}%)`);
    console.log(`   Domains: ${nc.unk_domains} (${(nc.unk_domains/nc.total_rows*100).toFixed(1)}%)`);
    
    // Check data consistency
    const consistency = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as rows_with_issues
      FROM "public2"."analytics_5_dimensions"
      WHERE "email_1_sent_count" < 0
         OR "email_2_sent_count" < 0
         OR "email_3_sent_count" < 0
         OR "replies_after_email_1" > "email_1_sent_count"
         OR "replies_after_email_2" > "email_2_sent_count"
         OR "replies_after_email_3" > "email_3_sent_count"
    `;
    
    if (consistency[0].rows_with_issues > 0) {
      console.log(`\n⚠️  Data consistency issues found: ${consistency[0].rows_with_issues} rows`);
    } else {
      console.log('\n✅ No data consistency issues found');
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('TEST COMPLETE\n');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    throw error;
  }
}

/**
 * Generate sample queries for dashboard
 */
function generateSampleQueries() {
  console.log('\n📝 SAMPLE DASHBOARD QUERIES\n');
  console.log('=' .repeat(60));
  console.log('Copy these queries to create Metabase cards:\n');
  
  const queries = [
    {
      name: 'Email Performance by SP',
      description: 'Shows email sent and reply rates grouped by Search Priority',
      sql: `
SELECT 
  sp,
  SUM(email_1_sent_count) as "Email 1 Sent",
  SUM(email_2_sent_count) as "Email 2 Sent", 
  SUM(email_3_sent_count) as "Email 3 Sent",
  SUM(replies_after_email_1) as "Replies after Email 1",
  SUM(replies_after_email_2) as "Replies after Email 2",
  SUM(replies_after_email_3) as "Replies after Email 3",
  CASE 
    WHEN SUM(email_1_sent_count) > 0 
    THEN ROUND(SUM(replies_after_email_1)::numeric / SUM(email_1_sent_count) * 100, 2)
    ELSE 0 
  END as "Email 1 Reply Rate %"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY sp
ORDER BY sp`
    },
    {
      name: 'Weekly Trend by Client',
      description: 'Shows weekly email volume trends for each client',
      sql: `
SELECT 
  week_start_date,
  client,
  SUM(email_1_sent_count + email_2_sent_count + email_3_sent_count) as "Total Emails",
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3) as "Total Replies",
  SUM(meetings_booked) as "Meetings Booked"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY week_start_date, client
ORDER BY week_start_date DESC, client`
    },
    {
      name: 'Provider Performance Comparison',
      description: 'Compares email providers by deliverability and engagement',
      sql: `
SELECT 
  email_provider,
  COUNT(DISTINCT client) as "Clients Using",
  SUM(email_1_sent_count) as "Emails Sent",
  SUM(replies_after_email_1) as "Genuine Replies",
  SUM(automated_replies_after_email_1) as "Auto Replies",
  SUM(error_replies_after_email_1) as "Errors",
  ROUND(
    SUM(replies_after_email_1)::numeric / NULLIF(SUM(email_1_sent_count), 0) * 100, 
    2
  ) as "Reply Rate %"
FROM public2.analytics_5_dimensions
GROUP BY email_provider
ORDER BY "Emails Sent" DESC`
    },
    {
      name: 'Domain Type Analysis',
      description: 'Analyzes performance by domain type (standard vs alternative)',
      sql: `
SELECT 
  domain_type,
  SUM(email_1_sent_count) as "Emails Sent",
  SUM(replies_after_email_1) as "Replies",
  SUM(error_replies_after_email_1) as "Errors",
  ROUND(
    SUM(error_replies_after_email_1)::numeric / NULLIF(SUM(email_1_sent_count), 0) * 100,
    2
  ) as "Error Rate %"
FROM public2.analytics_5_dimensions
GROUP BY domain_type
ORDER BY "Emails Sent" DESC`
    },
    {
      name: 'Funnel Performance Matrix',
      description: 'Shows all funnels with key metrics',
      sql: `
SELECT 
  funnel,
  COUNT(DISTINCT client) as "Active Clients",
  SUM(total_prospects_reached) as "Prospects Reached",
  SUM(email_1_sent_count) as "Email 1",
  SUM(email_2_sent_count) as "Email 2",
  SUM(email_3_sent_count) as "Email 3",
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3) as "Total Replies",
  SUM(meetings_booked) as "Meetings Booked"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY funnel
ORDER BY "Prospects Reached" DESC`
    }
  ];
  
  queries.forEach((q, i) => {
    console.log(`${i + 1}. ${q.name}`);
    console.log(`   ${q.description}`);
    console.log('   SQL:');
    console.log(q.sql.split('\n').map(line => '   ' + line).join('\n'));
    console.log();
  });
  
  console.log('=' .repeat(60));
  console.log('\n💡 To use these queries in Metabase:');
  console.log('   1. Create a new Question');
  console.log('   2. Choose "Native query"');
  console.log('   3. Paste the SQL');
  console.log('   4. Add to dashboard with filters for all 5 dimensions');
}

// Main execution
async function main() {
  console.log('🧪 5D ANALYTICS TESTING SUITE\n');
  
  try {
    await compareAnalytics();
    generateSampleQueries();
    
    console.log('\n✅ All tests completed successfully!');
  } catch (error) {
    console.error('\n❌ Testing failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { compareAnalytics, generateSampleQueries };