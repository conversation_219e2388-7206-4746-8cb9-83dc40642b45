/**
 * Inspect dashboard structure to understand how cards are organized
 */

const https = require('https');
const fs = require('fs');

const METABASE_URL = 'metabase.equalcollective.com';
const API_KEY = 'mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00=';
const DASHBOARD_ID = 2;

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: METABASE_URL,
      path: path,
      method: 'GET',
      headers: {
        'X-API-KEY': API_KEY
      }
    };
    
    https.get(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          reject(e);
        }
      });
    }).on('error', reject);
  });
}

async function inspect() {
  console.log('Inspecting Old Jeff Dashboard structure...\n');
  
  const dashboard = await makeRequest(`/api/dashboard/${DASHBOARD_ID}`);
  
  // Save full dashboard for inspection
  fs.writeFileSync('dashboard_full.json', JSON.stringify(dashboard, null, 2));
  console.log('Full dashboard saved to dashboard_full.json\n');
  
  console.log('Dashboard Structure:');
  console.log('===================');
  console.log('ID:', dashboard.id);
  console.log('Name:', dashboard.name);
  console.log('Collection:', dashboard.collection?.name || 'Root');
  
  // Check for tabs
  if (dashboard.tabs && dashboard.tabs.length > 0) {
    console.log(`\nTabs: ${dashboard.tabs.length}`);
    dashboard.tabs.forEach(tab => {
      console.log(`\n  Tab: "${tab.name}" (ID: ${tab.id})`);
      console.log(`    Cards: ${tab.cards?.length || 0}`);
      
      if (tab.cards) {
        tab.cards.forEach(card => {
          console.log(`      - Card ${card.card_id || card.id}: ${card.card?.name || 'Unknown'}`);
        });
      }
    });
  }
  
  // Check ordered_cards
  console.log(`\nOrdered Cards: ${dashboard.ordered_cards?.length || 0}`);
  if (dashboard.ordered_cards && dashboard.ordered_cards.length > 0) {
    dashboard.ordered_cards.forEach(oc => {
      console.log(`  - Card ${oc.card_id}: ${oc.card?.name || 'Unknown'}`);
    });
  }
  
  // Check dashcards
  console.log(`\nDashcards: ${dashboard.dashcards?.length || 0}`);
  if (dashboard.dashcards && dashboard.dashcards.length > 0) {
    dashboard.dashcards.forEach(dc => {
      const cardId = dc.card_id || dc.card?.id;
      const cardName = dc.card?.name || 'Unknown';
      const tabName = dc.tab?.name || 'No Tab';
      console.log(`  - Card ${cardId}: ${cardName} (Tab: ${tabName})`);
    });
  }
  
  // Check parameters (filters)
  console.log(`\nParameters (Filters): ${dashboard.parameters?.length || 0}`);
  if (dashboard.parameters) {
    dashboard.parameters.forEach(param => {
      console.log(`  - ${param.name} (${param.type})`);
      console.log(`    Slug: ${param.slug}`);
      console.log(`    Mappings: ${param.mappings?.length || 0} cards`);
    });
  }
  
  // List all top-level keys
  console.log('\nTop-level keys in dashboard:');
  Object.keys(dashboard).forEach(key => {
    const value = dashboard[key];
    const type = Array.isArray(value) ? `array[${value.length}]` : typeof value;
    console.log(`  - ${key}: ${type}`);
  });
}

inspect().catch(console.error);