# 1. Understanding the Aim of the Project

## Current State
- Analytics system that creates 6 tables but only uses 2
- No Search Priority (SP) dimension in analytics
- Timeouts on some data fetches (TagAnalytics, ProviderAnalytics)
- No dashboard-level filters
- Attribution broken (meeting attribution always "unknown")

## Desired State
Build a **fast analytics dashboard** with 5-dimensional aggregation:

### The 5 Dimensions:
1. **Client** - Which client does the inbox belong to which was used to send the email (Always available)
2. **Funnel** - Format: 6A, 7B, 99A. Combination of campaign series and single capital alphabet. Comes from Smartlead campaign name (Maybe available → UNK if missing)
3. **Email Provider** - Comes from Replit, all information is there via endpoint (Always available)
4. **Domain Type** - Domain TLD classification (Always available)
5. **Search Priority (SP)** - Comes from Jeff data by retracing SP using the email ID (Maybe available → UNK if missing)

## Metrics to Track (by Email Sequence)

### Core Email Metrics:
- **Email Sent Counts**: Email 1, 2, 3 Sent Count
- **Replies**: Replies After Email 1, 2, 3 Count  
- **Automated Replies**: Automated Replies After Email 1, 2, 3 Count
- **Error Replies**: Error Replies After Email 1, 2, 3 Count
- **Meeting Slots**: Meeting Slots Sent After Email 1, 2, 3
- **Total Meeting Slots Sent**
- **Meetings Booked**

### Email Type Breakdown (for each sequence 1, 2, 3):
- Email [N] Sent Personal Count
- Email [N] Sent Role Count  
- Email [N] Sent Work Count
- Email [N] Sent Unknown Count

### Total Metrics Needed:
- 6 base metrics × 3 email sequences = 18 metrics
- 4 email types × 3 sequences = 12 type breakdowns
- Plus: Total Meeting Slots Sent, Meetings Booked
- **Total: 32 metrics**

## Key Goals
1. **Create new analytics system** - Separate from old system, won't damage existing
2. **Add SP dimension** - Use email ID to trace back to SP from Jeff data
3. **Handle missing data gracefully** - Use 'UNK' for missing Funnel and SP
4. **Aggregate at 5 dimensions** - Client, Funnel, Email Provider, Domain Type, SP
5. **Track all 32 metrics** - Complete coverage of email performance

## Success Criteria
1. All 5 dimensions properly aggregated
2. All 32 metrics accurately calculated
3. Missing data handled with 'UNK' values
4. New system runs independently of old system
5. Clear data lineage from source to aggregation
