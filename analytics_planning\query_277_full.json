{"cache_invalidated_at": "2025-09-15T14:52:51.634779Z", "description": null, "archived": false, "view_count": 275, "collection_position": 2, "source_card_id": null, "table_id": null, "can_run_adhoc_query": true, "result_metadata": [{"display_name": "id", "semantic_type": "type/PK", "field_ref": ["field", "id", {"base-type": "type/Integer"}], "base_type": "type/Integer", "effective_type": "type/Integer", "database_type": "int4", "name": "id", "fingerprint": null}, {"display_name": "leadId", "field_ref": ["field", "leadId", {"base-type": "type/Integer"}], "base_type": "type/Integer", "effective_type": "type/Integer", "database_type": "int4", "name": "leadId", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 913, "nil%": 0}, "type": {"type/Number": {"min": 1294, "q1": 124498.94190918547, "q3": 6859800.852791834, "max": 7579291, "sd": 3222183.2546253237, "avg": 2831610.8835}}}}, {"display_name": "subject", "field_ref": ["field", "subject", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "subject", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 1133, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 126.7655}}}}, {"display_name": "body", "field_ref": ["field", "body", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "body", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 1932, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 2075.5085}}}}, {"display_name": "type", "semantic_type": "type/Category", "field_ref": ["field", "type", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "type", "fingerprint": {"global": {"distinct-count": 2, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 4.0325}}}}, {"display_name": "prospect_email_type", "semantic_type": "type/Category", "field_ref": ["field", "prospect_email_type", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "prospect_email_type", "fingerprint": {"global": {"distinct-count": 3, "nil%": 0.3645}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 3.326}}}}, {"display_name": "jeff_search_priority", "field_ref": ["field", "jeff_search_priority", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "jeff_search_priority", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 10, "nil%": 0.3645}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 2.1675}}}}, {"display_name": "dominant_search_priority", "field_ref": ["field", "dominant_search_priority", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "dominant_search_priority", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 10, "nil%": 0.3645}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 2.1675}}}}, {"display_name": "toEmailID", "field_ref": ["field", "toEmailID", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "toEmailID", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 316, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 1, "percent-state": 0, "average-length": 22.5805}}}}, {"display_name": "fromEmailID", "field_ref": ["field", "fromEmailID", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "fromEmailID", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 632, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 1, "percent-state": 0, "average-length": 30.7775}}}}, {"display_name": "time", "field_ref": ["field", "time", {"base-type": "type/DateTime"}], "base_type": "type/DateTime", "effective_type": "type/DateTime", "database_type": "timestamp", "name": "time", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 2000, "nil%": 0}, "type": {"type/DateTime": {"earliest": "2025-01-14T18:24:55.429Z", "latest": "2025-09-01T17:03:33.81Z"}}}}, {"display_name": "messageId", "field_ref": ["field", "messageId", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "messageId", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 2000, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 67.777}}}}, {"display_name": "campaingId", "field_ref": ["field", "campaingId", {"base-type": "type/Integer"}], "base_type": "type/Integer", "effective_type": "type/Integer", "database_type": "int4", "name": "campaingId", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 169, "nil%": 0}, "type": {"type/Number": {"min": 1297876, "q1": 1687272.5428658808, "q3": 2246545.2566400734, "max": 2433714, "sd": 333006.38069979165, "avg": 1944893.3195}}}}, {"display_name": "email_seq_number", "field_ref": ["field", "email_seq_number", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "email_seq_number", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 5, "nil%": 0.1105}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 0.8895}}}}, {"display_name": "open_count", "semantic_type": "type/Quantity", "field_ref": ["field", "open_count", {"base-type": "type/Integer"}], "base_type": "type/Integer", "effective_type": "type/Integer", "database_type": "int4", "name": "open_count", "fingerprint": {"global": {"distinct-count": 3, "nil%": 0.1415}, "type": {"type/Number": {"min": 0, "q1": 0, "q3": 0.29347606878016336, "max": 1, "sd": 0.024133196686197626, "avg": 0.0005824111822947001}}}}, {"display_name": "threadStartEmailId", "semantic_type": "type/Email", "field_ref": ["field", "threadStartEmailId", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "threadStartEmailId", "fingerprint": {"global": {"distinct-count": 603, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 1, "percent-state": 0, "average-length": 30.4875}}}}, {"display_name": "threadToEmailId", "field_ref": ["field", "threadToEmailId", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "threadToEmailId", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 316, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 1, "percent-state": 0, "average-length": 22.5805}}}}, {"display_name": "Campaign - campaingId__name", "field_ref": ["field", "Campaign - campaingId__name", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "Campaign - campaingId__name", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 165, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 36.559}}}}, {"display_name": "parentCampaignId", "field_ref": ["field", "parentCampaignId", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "parentCampaignId", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 5, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 4.021}}}}, {"display_name": "jeff_client_person", "field_ref": ["field", "jeff_client_person", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "jeff_client_person", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 11, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 12.38}}}}, {"display_name": "clientSmartLeadId", "field_ref": ["field", "clientSmartLeadId", {"base-type": "type/Integer"}], "base_type": "type/Integer", "effective_type": "type/Integer", "database_type": "int4", "name": "clientSmartLeadId", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 11, "nil%": 0}, "type": {"type/Number": {"min": 25946, "q1": 25948.515988840918, "q3": 39546.93635886756, "max": 108917, "sd": 14664.533200463202, "avg": 34976.3975}}}}, {"display_name": "jeff_client_name", "field_ref": ["field", "jeff_client_name", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "jeff_client_name", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 11, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 9.281}}}}, {"display_name": "jeff_campaign_code", "field_ref": ["field", "jeff_campaign_code", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "jeff_campaign_code", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 20, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 2.088}}}}, {"display_name": "jeff_email_status", "field_ref": ["field", "jeff_email_status", {"base-type": "type/Text"}], "base_type": "type/Text", "effective_type": "type/Text", "database_type": "text", "name": "jeff_email_status", "semantic_type": null, "fingerprint": {"global": {"distinct-count": 4, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 4.2165}}}}], "creator": {"email": "<EMAIL>", "first_name": "TechTeam", "last_login": "2025-09-11T14:20:40.840338Z", "is_qbnewb": false, "is_superuser": true, "id": 2, "last_name": "Admin", "date_joined": "2024-09-30T18:52:12.583269Z", "common_name": "TechTeam Admin"}, "initially_published_at": null, "can_write": true, "database_id": 4, "enable_embedding": false, "collection_id": 57, "query_type": "native", "name": "Smartlead eMails with Custom Reply Status", "last_query_start": "2025-09-15T18:21:06.068273Z", "dashboard_count": 0, "last_used_at": "2025-09-15T18:21:06.138766Z", "dashboard": null, "type": "model", "persisted": false, "average_query_time": 7856.422434367541, "creator_id": 2, "can_restore": false, "moderation_reviews": [], "updated_at": "2025-09-15T14:52:51.628911Z", "made_public_by_id": null, "embedding_params": null, "cache_ttl": null, "dataset_query": {"database": 4, "info": {"card-entity-id": "FcFlafB6KtNCS9C5EBhPJ"}, "type": "native", "native": {"template-tags": {"#271-seller-group-sellers-with-sp-classifications-prospects-w-emails": {"type": "card", "name": "#271-seller-group-sellers-with-sp-classifications-prospects-w-emails", "id": "24b17e74-15c8-4550-b038-88e4e530eb85", "display-name": "#271 Seller Group Sellers With Sp Classifications Prospects W Emails", "card-id": 271}}, "query": "SELECT\n  mv_email.\"id\" AS \"id\",\n  mv_email.\"leadId\" AS \"leadId\",\n  mv_email.\"subject\" AS \"subject\",\n  mv_email.\"body\" AS \"body\",\n  mv_email.\"type\" AS \"type\",\n  \"Prospects - email_type\".\"Seller Group - Prospects with eMail Classification__626da005\" AS \"prospect_email_type\",\n  \"Prospects - email_type\".jeff_search_priority,\n  \"Prospects - email_type\".dominant_search_priority,\n  mv_email.\"toEmailID\" AS \"toEmailID\",\n  mv_email.\"fromEmailID\" AS \"fromEmailID\",\n  mv_email.\"time\" AS \"time\",\n  mv_email.\"messageId\" AS \"messageId\",\n  mv_email.\"campaingId\" AS \"campaingId\",\n  mv_email.\"email_seq_number\" AS \"email_seq_number\",\n  mv_email.\"open_count\" AS \"open_count\",\n  mv_email.\"threadStartEmailId\" AS \"threadStartEmailId\",\n  mv_email.\"threadToEmailId\" AS \"threadToEmailId\",\n  \"Campaign - campaingId\".\"name\" AS \"Campaign - campaingId__name\",\n  \"Campaign - campaingId\".\"parentCampaignId\" AS \"parentCampaignId\",\n  \"Client - clientId\".\"name\" AS \"jeff_client_person\",\n  \"Client - clientId\".\"clientId\" AS \"clientSmartLeadId\",\n  \"Client - clientId\".\"businessName\" AS \"jeff_client_name\",\n  CASE \n    WHEN SUBSTRING(\"Campaign - campaingId\".\"name\" FROM '[0-9]{1,2}[A-Z]') IS NOT NULL \n      THEN SUBSTRING(\"Campaign - campaingId\".\"name\" FROM '[0-9]{1,2}[A-Z]')\n    WHEN \"Campaign - campaingId\".\"parentCampaignId\" IS NOT NULL \n      THEN 'HKP'\n    ELSE 'UNK'\n  END AS \"jeff_campaign_code\",\n  CASE\n    WHEN mv_email.\"type\" IN ('FORWARD', 'SENT') THEN mv_email.\"type\"\n    WHEN mv_email.\"type\" = 'REPLY' THEN CASE\n      WHEN LOWER(mv_email.\"messageId\") LIKE '%zendesk%'\n      OR LOWER(mv_email.\"messageId\") LIKE '%freshdesk%'\n      OR LOWER(mv_email.\"messageId\") LIKE '%helpscout%'\n      OR LOWER(mv_email.\"messageId\") LIKE '%hubspot%'\n      OR LOWER(mv_email.\"messageId\") LIKE '%intercom%'\n      OR LOWER(mv_email.\"body\") LIKE '%is an automated message%'\n      OR LOWER(mv_email.\"body\") LIKE '%important to us%'\n      OR LOWER(mv_email.\"body\") LIKE '%will get back to you as quickly as possible%'\n      OR LOWER(mv_email.\"body\") LIKE '%customer service%'\n      OR LOWER(mv_email.\"body\") LIKE '%auto message%'\n      OR LOWER(mv_email.\"body\") LIKE '%customer care%'\n      OR LOWER(mv_email.\"body\") LIKE '%will get back to you within%'\n      OR LOWER(mv_email.\"body\") LIKE '%auto-reply%'\n      OR LOWER(mv_email.\"body\") LIKE '%for your patience%'\n      OR LOWER(mv_email.\"body\") LIKE '%thank you for your request%'\n      OR LOWER(mv_email.\"body\") LIKE '%business hours are%'\n      OR LOWER(mv_email.\"body\") LIKE '%received your request.%'\n      OR LOWER(mv_email.\"body\") LIKE '%hear back from us within%'\n      OR LOWER(mv_email.\"body\") LIKE '%thank you for contacting%'\n      OR LOWER(mv_email.\"body\") LIKE '%thank you for reaching%'\n      OR LOWER(mv_email.\"body\") LIKE '%support experience%'\n      OR LOWER(mv_email.\"body\") LIKE '%support team%'\n      OR LOWER(mv_email.\"body\") LIKE '%**is an automated message**%' THEN 'REPLY_CS_AUTOMATED'\n      WHEN LOWER(mv_email.\"messageId\") LIKE '%mx.google.com%' THEN 'ERROR_REPLY'\n      WHEN (\n        LOWER(mv_email.\"messageId\") LIKE '%prod.outlook.com%'\n        OR LOWER(mv_email.\"messageId\") LIKE '%exchangelabs.com%'\n      )\n      AND (\n        LOWER(mv_email.\"body\") LIKE '%fail%'\n        OR LOWER(mv_email.\"body\") LIKE '%failure%'\n        OR LOWER(mv_email.\"body\") LIKE '%error%'\n        OR LOWER(mv_email.\"body\") LIKE '%action required recipient unknown%'\n        OR LOWER(mv_email.\"body\") LIKE '%your message to%'\n        OR LOWER(mv_email.\"body\") LIKE '%rejected%'\n      ) THEN 'ERROR_REPLY'\n      WHEN LOWER(mv_email.\"body\") LIKE '%automatically by mail delivery software%'\n      OR LOWER(mv_email.\"body\") LIKE '%delivery has failed%'\n      OR LOWER(mv_email.\"body\") LIKE '%created automatically by mail delivery software%'\n      OR LOWER(mv_email.\"body\") LIKE '%your message could not be delivered%'\n      OR LOWER(mv_email.\"body\") LIKE '%message could not be delivered%'\n      OR LOWER(mv_email.\"body\") LIKE '%recipient address rejected%'\n      OR (\n        LOWER(mv_email.\"body\") LIKE '%mail system%'\n        AND LOWER(mv_email.\"body\") LIKE '%rejected%'\n      ) THEN 'ERROR_REPLY'\n      ELSE 'REPLY'\n    END\n    ELSE NULL\n  END AS \"jeff_email_status\"\nFROM\n  mv_email_with_thread_start mv_email\n  LEFT JOIN \"public\".\"Campaign\" AS \"Campaign - campaingId\" ON mv_email.\"campaingId\" = \"Campaign - campaingId\".\"campaignId\"\n  LEFT JOIN \"public\".\"Client\" AS \"Client - clientId\" ON \"Campaign - campaingId\".\"clientId\" = \"Client - clientId\".\"clientId\"\n  LEFT JOIN {{#271-seller-group-sellers-with-sp-classifications-prospects-w-emails}} AS \"Prospects - email_type\" \n    ON mv_email.\"toEmailID\" = \"Prospects - email_type\".\"Seller Group - Prospects with eMail Classification__email\"\nLIMIT 1048575;"}}, "id": 277, "parameter_mappings": [], "can_manage_db": true, "display": "table", "archived_directly": false, "entity_id": "xgWNiHgXN0fEXbdofXTkA", "collection_preview": true, "last-edit-info": {"id": 13, "email": "<EMAIL>", "first_name": "<PERSON><PERSON><PERSON><PERSON>", "last_name": null, "timestamp": "2025-09-15T14:52:51.667043Z"}, "visualization_settings": {"table.pivot_column": "click_count", "table.cell_column": "leadId", "table.columns": [{"name": "id", "enabled": true}, {"name": "matchId", "enabled": true}, {"name": "leadId", "enabled": true}, {"name": "subject", "enabled": true}, {"name": "body", "enabled": true}, {"name": "type", "enabled": true}, {"name": "toEmailID", "enabled": true}, {"name": "fromEmailID", "enabled": true}, {"name": "time", "enabled": true}, {"name": "messageId", "enabled": true}, {"name": "campaingId", "enabled": true}, {"name": "click_count", "enabled": true}, {"name": "click_details", "enabled": true}, {"name": "email_seq_number", "enabled": true}, {"name": "open_count", "enabled": true}, {"name": "createdAt", "enabled": true}, {"name": "updatedAt", "enabled": true}, {"name": "threadStartEmailId", "enabled": true}, {"name": "Campaign - campaingId__name", "enabled": true}, {"name": "parentCampaignId", "enabled": true}, {"name": "jeff_client_person", "enabled": true}, {"name": "clientSmartLeadId", "enabled": true}, {"name": "jeff_client_name", "enabled": true}, {"name": "prospect_email_type", "enabled": true}, {"name": "jeff_campaign_code", "enabled": true}, {"name": "jeff_email_status", "enabled": true}, {"name": "threadToEmailId", "enabled": true}, {"name": "jeff_search_priority", "enabled": true}, {"name": "dominant_search_priority", "enabled": true}]}, "collection": {"authority_level": null, "description": "Internal Queries queries for <PERSON> Seller (Dashboard)", "archived": false, "slug": "internal_queries", "archive_operation_id": null, "name": "Internal Queries", "personal_owner_id": null, "type": null, "is_sample": false, "id": 57, "archived_directly": null, "entity_id": "9ZrtXNWPTaxuIobgYM1V-", "location": "/51/", "namespace": null, "is_personal": false, "created_at": "2025-09-04T18:14:04.124862Z"}, "metabase_version": "v0.54.5.4 (f262d88)", "parameters": [], "dashboard_id": null, "created_at": "2025-06-10T22:25:50.894005Z", "parameter_usage_count": 0, "public_uuid": null, "can_delete": false}