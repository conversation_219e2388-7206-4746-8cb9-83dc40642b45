const { completionFactory } = require("../../scrapeGPT/factory");
const fs = require("fs");
const { getScreenshotFromDomain } = require("../../../utils/getSSFromDomain");
require("dotenv").config();

async function validateImage(image2Base64, url, businessKeywords, options = {}) {
  try {
    if (!image2Base64) {
      return { error: "Missing Amazon image." };
    }
    const image1Base64 = await getScreenshotFromDomain(url);
    if (!image1Base64) {
      return { error: "Missing Url image." };
    }

    console.log("validation Image Starting for ", url, businessKeywords);
    const data = [
      // { "type": "text", "text": `Here are the brandedKeywords: ${businessKeywords}` },
      // { "type": "text", "text": `Here is the url ${url}` },
      {
        type: "image_url",
        image_url: {
          url: `data:image/jpeg;base64,${image1Base64}`,
        },
      },
      {
        type: "image_url",
        image_url: {
          url: `data:image/jpeg;base64,${image2Base64}`,
        },
      },
    ];

    const result = await completionFactory("match_image", data, null, false, {
      batchId: options?.serpBatchId || options?.batchId,
      directTags: ['sellerbot', 'imageValidation', 'logoValidation', 'getChatGPTResponse']
    });
    console.log("validation Image Ended for ", url, businessKeywords);
    return result;
  } catch (error) {
    console.error("Error in validateImage:", error);
    return { error: "Image validation failed." };
    // throw new Error(`Image validation failed: ${error.message}`);
  }
}

// Example usage with local files
// async function example() {
//     const image1Base64 = fs.readFileSync('1.jpg', { encoding: 'base64' });
//     const image2Base64 = fs.readFileSync('2.jpg', { encoding: 'base64' });

//     const result = await validateImage(image1Base64, image2Base64);
//     console.log(result);
// }
// example()
module.exports = {
  validateImage,
};
