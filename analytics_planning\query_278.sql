WITH email_analytics_base AS (
  SELECT 
    DATE(emails."time") AS email_date,
    DATE(DATE(emails."time") - EXTRACT(DOW FROM DATE(emails."time")) * INTERVAL '1 day') AS week_start_date,
    emails."jeff_client_name",
    emails."jeff_campaign_code",
    emails."clientSmartLeadId",
    emails."leadId",
    emails."email_seq_number",
    emails."type",
    emails."jeff_email_status",
    emails."threadStartEmailId",
    emails."prospect_email_type",
    
    -- Create flags for email sequences sent (avoids multiple scans)
    CASE WHEN emails."email_seq_number" = '1' AND emails."type" = 'SENT' THEN 1 ELSE 0 END AS email_1_sent,
    CASE WHEN emails."email_seq_number" = '2' AND emails."type" = 'SENT' THEN 1 ELSE 0 END AS email_2_sent,
    CASE WHEN emails."email_seq_number" = '3' AND emails."type" = 'SENT' THEN 1 ELSE 0 END AS email_3_sent,
    
    -- Create flags for email sequences sent by prospect type
    -- Email 1
    CASE WHEN emails."email_seq_number" = '1' AND emails."type" = 'SENT' AND emails."prospect_email_type" = 'personal' THEN 1 ELSE 0 END AS email_1_sent_personal,
    CASE WHEN emails."email_seq_number" = '1' AND emails."type" = 'SENT' AND emails."prospect_email_type" = 'role' THEN 1 ELSE 0 END AS email_1_sent_role,
    CASE WHEN emails."email_seq_number" = '1' AND emails."type" = 'SENT' AND emails."prospect_email_type" = 'work' THEN 1 ELSE 0 END AS email_1_sent_work,
    CASE WHEN emails."email_seq_number" = '1' AND emails."type" = 'SENT' AND (emails."prospect_email_type" IS NULL OR emails."prospect_email_type" = '') THEN 1 ELSE 0 END AS email_1_sent_unknown,
    
    -- Email 2
    CASE WHEN emails."email_seq_number" = '2' AND emails."type" = 'SENT' AND emails."prospect_email_type" = 'personal' THEN 1 ELSE 0 END AS email_2_sent_personal,
    CASE WHEN emails."email_seq_number" = '2' AND emails."type" = 'SENT' AND emails."prospect_email_type" = 'role' THEN 1 ELSE 0 END AS email_2_sent_role,
    CASE WHEN emails."email_seq_number" = '2' AND emails."type" = 'SENT' AND emails."prospect_email_type" = 'work' THEN 1 ELSE 0 END AS email_2_sent_work,
    CASE WHEN emails."email_seq_number" = '2' AND emails."type" = 'SENT' AND (emails."prospect_email_type" IS NULL OR emails."prospect_email_type" = '') THEN 1 ELSE 0 END AS email_2_sent_unknown,
    
    -- Email 3
    CASE WHEN emails."email_seq_number" = '3' AND emails."type" = 'SENT' AND emails."prospect_email_type" = 'personal' THEN 1 ELSE 0 END AS email_3_sent_personal,
    CASE WHEN emails."email_seq_number" = '3' AND emails."type" = 'SENT' AND emails."prospect_email_type" = 'role' THEN 1 ELSE 0 END AS email_3_sent_role,
    CASE WHEN emails."email_seq_number" = '3' AND emails."type" = 'SENT' AND emails."prospect_email_type" = 'work' THEN 1 ELSE 0 END AS email_3_sent_work,
    CASE WHEN emails."email_seq_number" = '3' AND emails."type" = 'SENT' AND (emails."prospect_email_type" IS NULL OR emails."prospect_email_type" = '') THEN 1 ELSE 0 END AS email_3_sent_unknown,
    
    -- Create flags for replies
    CASE WHEN emails."jeff_email_status" = 'REPLY' THEN 1 ELSE 0 END AS is_reply,
    CASE WHEN emails."jeff_email_status" = 'REPLY_CS_AUTOMATED' THEN 1 ELSE 0 END AS is_auto_reply,
    CASE WHEN emails."jeff_email_status" = 'ERROR_REPLY' THEN 1 ELSE 0 END AS is_error_reply
    
  FROM {{#277-smartlead-emails-with-custom-reply-status}} AS emails
),
lead_sequences AS (
  SELECT 
    "leadId",
    MAX(email_1_sent) AS has_email_1_sent,
    MAX(email_2_sent) AS has_email_2_sent,
    MAX(email_3_sent) AS has_email_3_sent
  FROM email_analytics_base
  GROUP BY "leadId"
)

SELECT 
  eab.email_date,
  eab.week_start_date,
  eab."jeff_client_name",
  eab."jeff_campaign_code" AS campaign_code,
  eab."clientSmartLeadId" AS client_id,
  
  -- Email counts (total)
  SUM(eab.email_1_sent) AS email_1_sent_count,
  SUM(eab.email_2_sent) AS email_2_sent_count,
  SUM(eab.email_3_sent) AS email_3_sent_count,
  
  -- Email 1 counts by prospect type
  SUM(eab.email_1_sent_personal) AS email_1_sent_personal_count,
  SUM(eab.email_1_sent_role) AS email_1_sent_role_count,
  SUM(eab.email_1_sent_work) AS email_1_sent_work_count,
  SUM(eab.email_1_sent_unknown) AS email_1_sent_unknown_count,
  
  -- Email 2 counts by prospect type
  SUM(eab.email_2_sent_personal) AS email_2_sent_personal_count,
  SUM(eab.email_2_sent_role) AS email_2_sent_role_count,
  SUM(eab.email_2_sent_work) AS email_2_sent_work_count,
  SUM(eab.email_2_sent_unknown) AS email_2_sent_unknown_count,
  
  -- Email 3 counts by prospect type
  SUM(eab.email_3_sent_personal) AS email_3_sent_personal_count,
  SUM(eab.email_3_sent_role) AS email_3_sent_role_count,
  SUM(eab.email_3_sent_work) AS email_3_sent_work_count,
  SUM(eab.email_3_sent_unknown) AS email_3_sent_unknown_count,
  
  -- Reply counts using pre-calculated flags
  SUM(CASE WHEN eab.is_reply = 1 AND ls.has_email_1_sent = 1 THEN 1 ELSE 0 END) AS replies_after_email_1_count,
  SUM(CASE WHEN eab.is_reply = 1 AND ls.has_email_2_sent = 1 THEN 1 ELSE 0 END) AS replies_after_email_2_count,
  SUM(CASE WHEN eab.is_reply = 1 AND ls.has_email_3_sent = 1 THEN 1 ELSE 0 END) AS replies_after_email_3_count,
  
  -- Auto reply counts
  SUM(CASE WHEN eab.is_auto_reply = 1 AND ls.has_email_1_sent = 1 THEN 1 ELSE 0 END) AS automated_replies_after_email_1_count,
  SUM(CASE WHEN eab.is_auto_reply = 1 AND ls.has_email_2_sent = 1 THEN 1 ELSE 0 END) AS automated_replies_after_email_2_count,
  SUM(CASE WHEN eab.is_auto_reply = 1 AND ls.has_email_3_sent = 1 THEN 1 ELSE 0 END) AS automated_replies_after_email_3_count,
  
  -- Error reply counts  
  SUM(CASE WHEN eab.is_error_reply = 1 AND ls.has_email_1_sent = 1 THEN 1 ELSE 0 END) AS error_replies_after_email_1_count,
  SUM(CASE WHEN eab.is_error_reply = 1 AND ls.has_email_2_sent = 1 THEN 1 ELSE 0 END) AS error_replies_after_email_2_count,
  SUM(CASE WHEN eab.is_error_reply = 1 AND ls.has_email_3_sent = 1 THEN 1 ELSE 0 END) AS error_replies_after_email_3_count,
  
  -- Meeting analytics (simplified)
  SUM(DISTINCT COALESCE(meetings.slots_sent_after_1, 0)) AS meeting_slots_sent_after_1,
  SUM(DISTINCT COALESCE(meetings.slots_sent_after_2, 0)) AS meeting_slots_sent_after_2,
  SUM(DISTINCT COALESCE(meetings.slots_sent_after_3, 0)) AS meeting_slots_sent_after_3,
  SUM(DISTINCT COALESCE(meetings.slots_sent_unknown, 0)) AS meeting_slots_sent_unknown,
  (SUM(DISTINCT COALESCE(meetings.slots_sent_after_1, 0)) + 
   SUM(DISTINCT COALESCE(meetings.slots_sent_after_2, 0)) + 
   SUM(DISTINCT COALESCE(meetings.slots_sent_after_3, 0)) + 
   SUM(DISTINCT COALESCE(meetings.slots_sent_unknown, 0))) AS total_meeting_slots_sent,
  SUM(DISTINCT COALESCE(meetings.booked, 0)) AS meetings_booked

FROM email_analytics_base eab
LEFT JOIN lead_sequences ls ON eab."leadId" = ls."leadId"
LEFT JOIN "public2"."MeetingsFromReplit" AS meetings 
  ON eab."clientSmartLeadId" = meetings."client" 
  AND eab.email_date = meetings."date"
  AND eab."jeff_campaign_code" = meetings."funnel"
  AND eab."threadStartEmailId" = meetings."fromEmailId"

GROUP BY 
  eab.email_date,
  eab.week_start_date,
  eab."jeff_client_name",
  eab."jeff_campaign_code",
  eab."clientSmartLeadId"

ORDER BY 
  eab.email_date DESC,
  eab."jeff_client_name",
  eab."jeff_campaign_code",
  eab."clientSmartLeadId";