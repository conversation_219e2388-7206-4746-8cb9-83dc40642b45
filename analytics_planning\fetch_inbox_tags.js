/**
 * Fetch inbox-tags data from jeffcrm to understand structure
 */

const https = require('https');
const fs = require('fs');

function fetchFromJeffCRM(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'jeffcrm.equalcollective.com',
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    https.get(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          reject(e);
        }
      });
    }).on('error', reject);
  });
}

async function analyzeInboxData() {
  console.log('Fetching inbox-tags data from JeffCRM...\n');
  
  try {
    const inboxes = await fetchFromJeffCRM('/api/public/inbox-tags');
    
    console.log(`Total inbox accounts: ${inboxes.length}\n`);
    
    // Sample first 3 inboxes for structure
    console.log('=== SAMPLE INBOX DATA (First 3) ===\n');
    inboxes.slice(0, 3).forEach((inbox, i) => {
      console.log(`\n--- Inbox ${i + 1} ---`);
      console.log(JSON.stringify(inbox, null, 2));
    });
    
    // Analyze structure
    console.log('\n\n=== FIELD ANALYSIS ===\n');
    
    // Get all unique fields
    const allFields = new Set();
    inboxes.forEach(inbox => {
      Object.keys(inbox).forEach(key => allFields.add(key));
    });
    
    console.log('All fields found:');
    Array.from(allFields).sort().forEach(field => {
      console.log(`  - ${field}`);
    });
    
    // Analyze key fields for our dimensions
    console.log('\n=== KEY FIELDS FOR ANALYTICS ===\n');
    
    // 1. Email Provider Analysis
    console.log('1. EMAIL PROVIDER DIMENSION:');
    const providerTypes = {};
    inboxes.forEach(inbox => {
      const type = inbox.type || 'UNKNOWN';
      providerTypes[type] = (providerTypes[type] || 0) + 1;
    });
    console.log('  Provider types distribution:');
    Object.entries(providerTypes)
      .sort((a, b) => b[1] - a[1])
      .forEach(([type, count]) => {
        console.log(`    - ${type}: ${count} (${(count/inboxes.length*100).toFixed(1)}%)`);
      });
    
    // Check for detailed provider info
    console.log('\n  Detailed provider info (first 10 with domain.currentProvider):');
    let providerCount = 0;
    inboxes.forEach(inbox => {
      if (inbox.domain?.currentProvider && providerCount < 10) {
        console.log(`    - ${inbox.email} → Provider: ${inbox.domain.currentProvider.name || inbox.domain.currentProvider}`);
        providerCount++;
      }
    });
    
    // 2. Domain Type Analysis
    console.log('\n2. DOMAIN TYPE DIMENSION:');
    const domainTypes = {};
    const uniqueDomains = new Set();
    inboxes.forEach(inbox => {
      if (inbox.email) {
        const domain = inbox.email.split('@')[1];
        if (domain) {
          uniqueDomains.add(domain);
          const tld = domain.substring(domain.lastIndexOf('.'));
          domainTypes[tld] = (domainTypes[tld] || 0) + 1;
        }
      }
    });
    console.log(`  Unique domains: ${uniqueDomains.size}`);
    console.log('  Top 15 TLDs:');
    Object.entries(domainTypes)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 15)
      .forEach(([tld, count]) => {
        console.log(`    - ${tld}: ${count} (${(count/inboxes.length*100).toFixed(1)}%)`);
      });
    
    // 3. Client mapping
    console.log('\n3. CLIENT MAPPING:');
    const clientIds = new Set();
    inboxes.forEach(inbox => {
      if (inbox.clientId) clientIds.add(inbox.clientId);
    });
    console.log(`  Unique client IDs: ${clientIds.size}`);
    console.log('  Sample client IDs:', Array.from(clientIds).slice(0, 10));
    
    // 4. SmartLead ID for joining
    console.log('\n4. SMARTLEAD ID (for joining):');
    const hasSmartleadId = inboxes.filter(i => i.smartleadId).length;
    console.log(`  Inboxes with smartleadId: ${hasSmartleadId} (${(hasSmartleadId/inboxes.length*100).toFixed(1)}%)`);
    console.log('  Sample smartleadIds:', inboxes.filter(i => i.smartleadId).slice(0, 5).map(i => i.smartleadId));
    
    // 5. Tags analysis
    console.log('\n5. TAGS ANALYSIS:');
    const tagCounts = {};
    let inboxesWithTags = 0;
    inboxes.forEach(inbox => {
      if (inbox.tags && inbox.tags.length > 0) {
        inboxesWithTags++;
        inbox.tags.forEach(tag => {
          const tagName = tag.name || tag;
          tagCounts[tagName] = (tagCounts[tagName] || 0) + 1;
        });
      }
    });
    console.log(`  Inboxes with tags: ${inboxesWithTags} (${(inboxesWithTags/inboxes.length*100).toFixed(1)}%)`);
    console.log('  Top 10 tags:');
    Object.entries(tagCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .forEach(([tag, count]) => {
        console.log(`    - ${tag}: ${count} inboxes`);
      });
    
    // 6. Check for additional useful fields
    console.log('\n6. ADDITIONAL FIELDS:');
    const sampleInbox = inboxes.find(i => i.domain?.currentProvider) || inboxes[0];
    if (sampleInbox.domain) {
      console.log('  Domain object structure:');
      console.log(JSON.stringify(sampleInbox.domain, null, 4));
    }
    
    // Create data directory if it doesn't exist
    if (!fs.existsSync('data')) {
      fs.mkdirSync('data');
    }
    
    // Save sample data
    fs.writeFileSync('data/inboxes_sample.json', JSON.stringify(inboxes.slice(0, 10), null, 2));
    console.log('\n✅ Sample data saved to data/inboxes_sample.json');
    
    // Summary for mapping
    console.log('\n=== MAPPING SUMMARY FOR OUR 5 DIMENSIONS ===\n');
    console.log('For Email Provider Dimension:');
    console.log('  - Primary field: inbox.type (GMAIL/SMTP/OUTLOOK)');
    console.log('  - Secondary field: inbox.domain.currentProvider.name');
    console.log('  - Table: InboxesFromReplit stores this as provider_name');
    console.log('\nFor Domain Type Dimension:');
    console.log('  - Extract from: inbox.email (split by @, get TLD)');
    console.log('  - Classify TLDs into: standard (.com/.net/.org), alternative, other');
    console.log('\nFor Joining with Meetings/Emails:');
    console.log('  - Join field: inbox.email matches meetings.emailHistory[].from');
    console.log('  - Join field: inbox.email matches Email.fromEmailID');
    console.log('\nFor Client Mapping:');
    console.log('  - Use: inbox.clientId to verify client ownership');
    console.log('  - Use: inbox.smartleadId for SmartLead integration');
    console.log('\nFor Tags (if needed):');
    console.log('  - inbox.tags[] array contains tag objects');
    console.log('  - InboxTagsFromReplit table stores these relationships');
    
  } catch (error) {
    console.error('Error fetching inbox data:', error);
  }
}

analyzeInboxData();