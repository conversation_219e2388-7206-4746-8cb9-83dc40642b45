/**
 * AI Metadata Helper for SellerBot
 *
 * This utility helps generate consistent metadata for AI requests with the simplified
 * tagging format: [sellerbot, service_name, function_name]
 *
 * Only essential business tags are included: validator:image, validator:htmlText, batchId.
 * All other metadata tags have been removed per tagging cleanup requirements.
 */

const path = require('path');

class AIMetadataHelper {
    constructor() {
        // Simplified constructor - no session tracking
    }

    /**
     * Get calling function information from stack trace
     */
    getCallingFunction() {
        const stack = new Error().stack;
        const stackLines = stack.split('\n');
        
        // Skip the first few lines (Error, this function, and immediate caller)
        for (let i = 3; i < stackLines.length; i++) {
            const line = stackLines[i];
            if (line.includes('at ') && !line.includes('node_modules')) {
                // Extract function name and file
                const match = line.match(/at\s+([^\s]+)\s+\(([^)]+)\)/);
                if (match) {
                    const functionName = match[1];
                    const filePath = match[2];
                    const fileName = path.basename(filePath);
                    return {
                        function: functionName,
                        file: fileName,
                        path: filePath
                    };
                }
                
                // Handle anonymous functions
                const anonymousMatch = line.match(/at\s+([^(]+)/);
                if (anonymousMatch) {
                    const location = anonymousMatch[1].trim();
                    return {
                        function: 'anonymous',
                        file: path.basename(location),
                        path: location
                    };
                }
            }
        }
        
        return {
            function: 'unknown',
            file: 'unknown',
            path: 'unknown'
        };
    }

    /**
     * Generate standardized tags in the format: [sellerbot, service_name, function_name]
     * Additional tags follow the pattern: key:value
     * Only approved tags: validator:image, validator:htmlText, batchId
     */
    generateStandardizedTags(serviceName, functionName, options = {}) {
        const caller = this.getCallingFunction();

        // Core standardized tags: [sellerbot, service_name, function_name]
        const tags = [
            'sellerbot',
            serviceName || 'unknown_service',
            functionName || caller.function || 'unknown_function'
        ];

        // Only add approved business tags from the tagging sheet
        const businessTags = [];

        // Only include batchId from approved list
        if (options.batchId) businessTags.push(`batchId:${options.batchId}`);

        // Add custom tags (should only contain approved validator tags: validator:image, validator:htmlText)
        if (options.customTags && Array.isArray(options.customTags)) {
            businessTags.push(...options.customTags);
        }

        return [...tags, ...businessTags];
    }

    /**
     * Generate LiteLLM compatible request body with standardized metadata
     */
    generateLiteLLMRequestBody(messages, serviceName, functionName, options = {}) {
        const tags = this.generateStandardizedTags(serviceName, functionName, options);

        // Only include valid OpenAI API parameters
        const requestBody = {
            model: options.model || 'gpt-4o',
            messages: messages,
            user: options.userId || options.user || 'system',
            metadata: {
                tags: tags
            }
        };

        // Add optional parameters only if they are provided
        if (options.temperature !== undefined) requestBody.temperature = options.temperature;
        if (options.max_tokens !== undefined) requestBody.max_tokens = options.max_tokens;
        if (options.top_p !== undefined) requestBody.top_p = options.top_p;
        if (options.frequency_penalty !== undefined) requestBody.frequency_penalty = options.frequency_penalty;
        if (options.presence_penalty !== undefined) requestBody.presence_penalty = options.presence_penalty;
        if (options.stop !== undefined) requestBody.stop = options.stop;
        if (options.stream !== undefined) requestBody.stream = options.stream;
        if (options.seed !== undefined) requestBody.seed = options.seed;
        if (options.tools !== undefined) requestBody.tools = options.tools;
        if (options.tool_choice !== undefined) requestBody.tool_choice = options.tool_choice;
        if (options.response_format !== undefined) requestBody.response_format = options.response_format;

        return requestBody;
    }

    /**
     * Generate headers for HTTP requests with metadata
     */
    generateHeaders() {
        return {
            'X-Service': 'SellerBot'
        };
    }



    /**
     * Create a scoped metadata generator for a specific service
     */
    createServiceGenerator(serviceName) {
        return {
            generateTags: (functionName, options = {}) => {
                return this.generateStandardizedTags(serviceName, functionName, options);
            },
            generateRequestBody: (messages, functionName, options = {}) => {
                return this.generateLiteLLMRequestBody(messages, serviceName, functionName, options);
            },
            generateHeaders: () => {
                return this.generateHeaders();
            }
        };
    }
}

// Create singleton instance
const aiMetadataHelper = new AIMetadataHelper();

// Pre-configured generators for SellerBot services
const serviceGenerators = {
    scrapeGPT: aiMetadataHelper.createServiceGenerator('scrapeGPT'),
    leadGeneration: aiMetadataHelper.createServiceGenerator('leadGeneration'),
    emailAnalysis: aiMetadataHelper.createServiceGenerator('emailAnalysis'),
    assistant: aiMetadataHelper.createServiceGenerator('assistant'),
    centralizedAI: aiMetadataHelper.createServiceGenerator('centralizedAI'),
    portkeyWrapper: aiMetadataHelper.createServiceGenerator('portkeyWrapper'),
    csvProcessor: aiMetadataHelper.createServiceGenerator('csvProcessor'),
    dataAnalysis: aiMetadataHelper.createServiceGenerator('dataAnalysis')
};

module.exports = {
    AIMetadataHelper,
    aiMetadataHelper,
    serviceGenerators
};
