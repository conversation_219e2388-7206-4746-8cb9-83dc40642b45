const { getChatGPTResponse } = require("../../scrapeGPT/request");
const axios = require("axios");
const cheerio = require("cheerio");
const { getOrFetchHtml } = require("../../../utils/fileCache");
const fs = require("fs");

async function validateHtmlText(url, businessKeywords, options = {}) {
  try {
    console.log("Validating HTML text for URL:", url, businessKeywords);
    // Fetch the HTML content

    const htmlContent = await getOrFetchHtml(url);

    // Extract text content using cheerio and limit to 10000 chars
    const $ = cheerio.load(htmlContent);
    const textContent = $("body")
      .text()
      .replace(/\s+/g, " ")
      .trim()
      .substring(0, 10000);

    // Get the prompt template
    const system_prompt = fs.readFileSync(`${__dirname}/../../scrapeGPT/matchText.md`, "utf8");
    const user_prompt = JSON.stringify({
      textContent: textContent,
      businessKeywords: businessKeywords,
      url: url,
    });

    const result = await getChatGPTResponse(system_prompt, user_prompt, {
      batchId: options?.serpBatchId || options?.batchId,
      directTags: ['sellerbot', 'htmlValidation', 'keywordsValidation', 'getChatGPTResponse']
    });
    return result;
  } catch (error) {
    console.error("ValidateHtmlText Error:", error);
    return { message: "False", textContent: "" };
  }
}

function exampleUsage() {
  const data = {
    Keywords: "Cheesers | Wisconsin",
    Confidence: "5",
    Confident_Url:
      "https://www.cheesers.com/?srsltid=AfmBOopP01vW2dStfQtQZfwuk7v6FLP8W5rGbTUXChOjwSigsfftZoQk",
    Seller_Url: "https://www.amazon.com/sp?seller=A3ULXFX63XERAM",
    Individual: '"{""checkSerpOrganicSnippet"":1',
  };

  validateHtmlText(data.Confident_Url, [data.Keywords])
    .then((result) => {
      console.log("Validation Result:", result);
    })
    .catch((error) => {
      console.error("Error:", error);
    });
}
// exampleUsage();
module.exports = validateHtmlText;
