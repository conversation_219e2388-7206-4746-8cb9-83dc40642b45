/**
 * Fetch Query 278 - which Query 291 depends on
 */

const https = require('https');
const fs = require('fs');

const METABASE_URL = 'metabase.equalcollective.com';
const API_KEY = 'mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00=';

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: METABASE_URL,
      path: path,
      method: 'GET',
      headers: {
        'X-API-KEY': API_KEY
      }
    };
    
    https.get(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          reject(e);
        }
      });
    }).on('error', reject);
  });
}

async function analyzeQuery278() {
  console.log('Fetching Query 278 (which 291 depends on)...\n');
  
  try {
    const query = await makeRequest('/api/card/278');
    
    console.log('Query Name:', query.name);
    console.log('Description:', query.description || 'None');
    console.log('Type:', query.query_type);
    console.log();
    
    // Check dependencies
    if (query.dataset_query?.query?.['source-table']) {
      const sourceTable = query.dataset_query.query['source-table'];
      console.log('Source:', sourceTable);
      
      if (typeof sourceTable === 'string' && sourceTable.startsWith('card__')) {
        const dependencyId = sourceTable.replace('card__', '');
        console.log(`\nDepends on Query ${dependencyId}`);
        
        // Fetch dependency
        const dependency = await makeRequest(`/api/card/${dependencyId}`);
        console.log(`  → ${dependency.name}`);
        
        // If 278 depends on 277, that's the critical path
        if (dependencyId === '277') {
          console.log('\n🔴 CRITICAL: Query 278 → 277 → mv_email_with_thread_start');
          console.log('This is why it might timeout!');
        }
      }
    }
    
    // Check if it's native SQL
    if (query.dataset_query?.native?.query) {
      const sql = query.dataset_query.native.query;
      console.log('\nSQL Query Length:', sql.length, 'characters');
      
      // Save SQL
      fs.writeFileSync('query_278.sql', sql);
      console.log('✅ SQL saved to query_278.sql');
      
      // Look for performance issues
      console.log('\n=== PERFORMANCE ANALYSIS ===');
      
      const hasLargeAggregation = /GROUP BY.*COUNT|SUM.*GROUP BY/i.test(sql);
      const hasMultipleJoins = (sql.match(/JOIN/gi) || []).length;
      
      console.log('Number of JOINs:', hasMultipleJoins);
      console.log('Has large aggregation:', hasLargeAggregation);
      
      if (sql.includes('mv_email_with_thread_start')) {
        console.log('⚠️  Uses mv_email_with_thread_start (large table)');
      }
      
      if (sql.includes('#271') || sql.includes('271')) {
        console.log('⚠️  References Query 271 (170K+ records)');
      }
    }
    
    // Save full query
    fs.writeFileSync('query_278_full.json', JSON.stringify(query, null, 2));
    
    console.log('\n=== DEPENDENCY CHAIN ===');
    console.log('Query 291 (Funnel & Client Analytics)');
    console.log('  ↓ depends on');
    console.log('Query 278 (Smartlead Analytics)');
    console.log('  ↓ depends on');
    console.log('Query 277 (Base with SP) [if confirmed]');
    console.log('  ↓ depends on');
    console.log('mv_email_with_thread_start + Query 271');
    
    console.log('\n=== TIMEOUT RISK ===');
    console.log('If any query in this chain times out, FunnelClientAnalytics fails.');
    console.log('The deeper the dependency, the higher the timeout risk.');
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

analyzeQuery278();