/**
 * Step 1: Process email data and aggregate with 5 dimensions
 * Applies all classification logic and creates aggregated metrics
 */

const prisma = require("../../database/prisma/getPrismaClient");
const fs = require('fs');
const path = require('path');

// Load lookup maps
const dataDir = path.join(__dirname, 'data');

/**
 * Email status classification (from Query 277)
 */
function classifyEmailStatus(email) {
  if (!email.type) return null;
  
  if (email.type === 'SENT' || email.type === 'FORWARD') {
    return email.type;
  }
  
  if (email.type === 'REPLY') {
    const messageId = (email.messageId || '').toLowerCase();
    const body = (email.body || '').toLowerCase();
    
    // CS Automated patterns
    const csMessagePatterns = ['zendesk', 'freshdesk', 'helpscout', 'hubspot', 'intercom'];
    const csBodyPatterns = [
      'is an automated message',
      'important to us',
      'will get back to you as quickly as possible',
      'customer service',
      'auto message',
      'customer care',
      'will get back to you within',
      'auto-reply',
      'for your patience',
      'thank you for your request',
      'business hours are',
      'received your request.',
      'hear back from us within',
      'thank you for contacting',
      'thank you for reaching',
      'support experience',
      'support team'
    ];
    
    // Check for CS automated
    if (csMessagePatterns.some(pattern => messageId.includes(pattern)) ||
        csBodyPatterns.some(pattern => body.includes(pattern))) {
      return 'REPLY_CS_AUTOMATED';
    }
    
    // Error reply patterns
    if (messageId.includes('mx.google.com')) {
      return 'ERROR_REPLY';
    }
    
    if ((messageId.includes('prod.outlook.com') || messageId.includes('exchangelabs.com')) &&
        (body.includes('fail') || body.includes('failure') || body.includes('error') ||
         body.includes('action required recipient unknown') || body.includes('your message to') ||
         body.includes('rejected'))) {
      return 'ERROR_REPLY';
    }
    
    const errorBodyPatterns = [
      'automatically by mail delivery software',
      'delivery has failed',
      'created automatically by mail delivery software',
      'your message could not be delivered',
      'message could not be delivered',
      'recipient address rejected'
    ];
    
    if (errorBodyPatterns.some(pattern => body.includes(pattern)) ||
        (body.includes('mail system') && body.includes('rejected'))) {
      return 'ERROR_REPLY';
    }
    
    return 'REPLY';
  }
  
  return null;
}

/**
 * Extract funnel code from campaign name
 */
function extractFunnelCode(campaignName, parentCampaignId) {
  if (!campaignName) return 'UNK';
  
  const match = campaignName.match(/(\d{1,2}[A-Z])/);
  if (match) return match[1];
  if (parentCampaignId) return 'HKP';
  return 'UNK';
}

/**
 * Get week start date (Monday)
 */
function getWeekStartDate(date) {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1); // adjust when day is sunday
  const monday = new Date(d.setDate(diff));
  monday.setHours(0, 0, 0, 0);
  return monday.toISOString().split('T')[0];
}

/**
 * Process emails and create aggregated data
 */
async function processEmails() {
  console.log('📊 Processing emails and creating aggregations...\n');
  
  try {
    // Load lookup maps
    console.log('📥 Loading lookup maps...');
    const spMap = JSON.parse(fs.readFileSync(path.join(dataDir, 'sp_map.json'), 'utf8'));
    const providerMap = JSON.parse(fs.readFileSync(path.join(dataDir, 'provider_map.json'), 'utf8'));
    const meetingsData = JSON.parse(fs.readFileSync(path.join(dataDir, 'meetings_by_client.json'), 'utf8'));
    
    console.log(`   SP entries: ${Object.keys(spMap).length}`);
    console.log(`   Provider entries: ${Object.keys(providerMap).length}`);
    console.log(`   Meetings data: ${Object.keys(meetingsData).length} clients\n`);
    
    // Get date range for processing (last 90 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 90);
    
    console.log(`📅 Processing emails from ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}\n`);
    
    // Fetch emails with campaign and client info
    console.log('🔍 Fetching emails from database...');
    const emails = await prisma.$queryRaw`
      SELECT 
        e."id",
        e."leadId",
        e."toEmailID",
        e."fromEmailID",
        e."type",
        e."time",
        e."messageId",
        e."body",
        e."subject",
        e."email_seq_number",
        e."campaingId",
        e."threadStartEmailId",
        c."name" as campaign_name,
        c."parentCampaignId",
        c."clientId",
        cl."businessName" as client_name,
        cl."clientId" as client_smartlead_id
      FROM "Email" e
      LEFT JOIN "Campaign" c ON e."campaingId" = c."campaignId"
      LEFT JOIN "Client" cl ON c."clientId" = cl."clientId"
      WHERE e."time" >= ${startDate}
        AND e."time" <= ${endDate}
      ORDER BY e."time"
    `;
    
    console.log(`   ✅ Fetched ${emails.length} emails\n`);
    
    // Process emails and aggregate
    console.log('⚙️  Processing and aggregating...');
    const aggregations = {};
    const leadSequences = {}; // Track what emails were sent to each lead
    
    let processed = 0;
    
    for (const email of emails) {
      processed++;
      if (processed % 10000 === 0) {
        console.log(`   Processed ${processed}/${emails.length} emails...`);
      }
      
      // Get dimensions
      const weekStart = getWeekStartDate(email.time);
      const client = email.client_name || 'UNK';
      const funnel = extractFunnelCode(email.campaign_name, email.parentCampaignId);
      
      // Get SP from lookup
      const toEmail = (email.toEmailID || '').toLowerCase();
      const spData = spMap[toEmail] || { sp: 'UNK', email_type: 'unknown' };
      const sp = spData.sp;
      const prospectType = spData.email_type;
      
      // Get provider from lookup
      const fromEmail = (email.fromEmailID || '').toLowerCase();
      const providerData = providerMap[fromEmail] || { provider: 'UNK', domain_type: 'unknown' };
      const provider = providerData.provider;
      const domainType = providerData.domain_type;
      
      // Create aggregation key
      const key = `${weekStart}|${client}|${funnel}|${provider}|${domainType}|${sp}`;
      
      // Initialize aggregation entry
      if (!aggregations[key]) {
        aggregations[key] = {
          week_start_date: weekStart,
          client,
          funnel,
          email_provider: provider,
          domain_type: domainType,
          sp,
          
          // Email sent counts
          email_1_sent_count: 0,
          email_2_sent_count: 0,
          email_3_sent_count: 0,
          
          // By prospect type
          email_1_sent_personal: 0,
          email_1_sent_role: 0,
          email_1_sent_work: 0,
          email_1_sent_unknown: 0,
          
          email_2_sent_personal: 0,
          email_2_sent_role: 0,
          email_2_sent_work: 0,
          email_2_sent_unknown: 0,
          
          email_3_sent_personal: 0,
          email_3_sent_role: 0,
          email_3_sent_work: 0,
          email_3_sent_unknown: 0,
          
          // Reply counts
          replies_after_email_1: 0,
          replies_after_email_2: 0,
          replies_after_email_3: 0,
          
          automated_replies_after_email_1: 0,
          automated_replies_after_email_2: 0,
          automated_replies_after_email_3: 0,
          
          error_replies_after_email_1: 0,
          error_replies_after_email_2: 0,
          error_replies_after_email_3: 0,
          
          // Meeting counts (will be added later)
          meeting_slots_sent_after_1: 0,
          meeting_slots_sent_after_2: 0,
          meeting_slots_sent_after_3: 0,
          meeting_slots_sent_unknown: 0,
          meetings_booked: 0,
          
          // Unique leads
          unique_leads: new Set()
        };
      }
      
      // Track lead sequences
      if (email.leadId) {
        aggregations[key].unique_leads.add(email.leadId);
        
        if (!leadSequences[email.leadId]) {
          leadSequences[email.leadId] = {
            has_email_1: false,
            has_email_2: false,
            has_email_3: false
          };
        }
      }
      
      // Process based on email type
      const emailStatus = classifyEmailStatus(email);
      const emailSeq = email.email_seq_number;
      
      if (email.type === 'SENT' && emailSeq) {
        // Track sent emails
        if (emailSeq === '1') {
          aggregations[key].email_1_sent_count++;
          if (email.leadId) leadSequences[email.leadId].has_email_1 = true;
          
          // By prospect type
          if (prospectType === 'personal') aggregations[key].email_1_sent_personal++;
          else if (prospectType === 'role') aggregations[key].email_1_sent_role++;
          else if (prospectType === 'work') aggregations[key].email_1_sent_work++;
          else aggregations[key].email_1_sent_unknown++;
        }
        else if (emailSeq === '2') {
          aggregations[key].email_2_sent_count++;
          if (email.leadId) leadSequences[email.leadId].has_email_2 = true;
          
          // By prospect type
          if (prospectType === 'personal') aggregations[key].email_2_sent_personal++;
          else if (prospectType === 'role') aggregations[key].email_2_sent_role++;
          else if (prospectType === 'work') aggregations[key].email_2_sent_work++;
          else aggregations[key].email_2_sent_unknown++;
        }
        else if (emailSeq === '3') {
          aggregations[key].email_3_sent_count++;
          if (email.leadId) leadSequences[email.leadId].has_email_3 = true;
          
          // By prospect type
          if (prospectType === 'personal') aggregations[key].email_3_sent_personal++;
          else if (prospectType === 'role') aggregations[key].email_3_sent_role++;
          else if (prospectType === 'work') aggregations[key].email_3_sent_work++;
          else aggregations[key].email_3_sent_unknown++;
        }
      }
      
      // Process replies with attribution
      if (emailStatus === 'REPLY' || emailStatus === 'REPLY_CS_AUTOMATED' || emailStatus === 'ERROR_REPLY') {
        const leadSeq = leadSequences[email.leadId] || {};
        
        // Attribute to the highest email sequence sent
        if (leadSeq.has_email_3) {
          if (emailStatus === 'REPLY') aggregations[key].replies_after_email_3++;
          else if (emailStatus === 'REPLY_CS_AUTOMATED') aggregations[key].automated_replies_after_email_3++;
          else if (emailStatus === 'ERROR_REPLY') aggregations[key].error_replies_after_email_3++;
        }
        else if (leadSeq.has_email_2) {
          if (emailStatus === 'REPLY') aggregations[key].replies_after_email_2++;
          else if (emailStatus === 'REPLY_CS_AUTOMATED') aggregations[key].automated_replies_after_email_2++;
          else if (emailStatus === 'ERROR_REPLY') aggregations[key].error_replies_after_email_2++;
        }
        else if (leadSeq.has_email_1) {
          if (emailStatus === 'REPLY') aggregations[key].replies_after_email_1++;
          else if (emailStatus === 'REPLY_CS_AUTOMATED') aggregations[key].automated_replies_after_email_1++;
          else if (emailStatus === 'ERROR_REPLY') aggregations[key].error_replies_after_email_1++;
        }
      }
    }
    
    console.log(`   ✅ Created ${Object.keys(aggregations).length} aggregation entries\n`);
    
    // Add meeting data
    console.log('📅 Adding meeting data to aggregations...');
    
    for (const [key, agg] of Object.entries(aggregations)) {
      const clientId = agg.client; // This needs to be mapped to smartlead ID
      const funnel = agg.funnel;
      
      // Look up meetings for this client/funnel
      if (meetingsData[clientId] && meetingsData[clientId][funnel]) {
        const meetings = meetingsData[clientId][funnel];
        agg.meeting_slots_sent_after_1 = meetings.slots_sent_after_1 || 0;
        agg.meeting_slots_sent_after_2 = meetings.slots_sent_after_2 || 0;
        agg.meeting_slots_sent_after_3 = meetings.slots_sent_after_3 || 0;
        agg.meeting_slots_sent_unknown = meetings.slots_sent_unknown || 0;
        agg.meetings_booked = meetings.booked || 0;
      }
    }
    
    // Convert sets to counts
    const finalAggregations = Object.values(aggregations).map(agg => ({
      ...agg,
      total_prospects_reached: agg.unique_leads.size,
      unique_leads: undefined // Remove the Set
    }));
    
    // Save processed data
    fs.writeFileSync(
      path.join(dataDir, 'processed_aggregations.json'),
      JSON.stringify(finalAggregations, null, 2)
    );
    
    console.log(`\n✅ Processing complete!`);
    console.log(`   Total aggregations: ${finalAggregations.length}`);
    console.log(`   Data saved to: ${path.join(dataDir, 'processed_aggregations.json')}`);
    
    return finalAggregations;
    
  } catch (error) {
    console.error('❌ Error processing emails:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  processEmails()
    .then(() => process.exit(0))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { processEmails, classifyEmailStatus, extractFunnelCode };