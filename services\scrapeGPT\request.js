const { OpenAI } = require("openai");
require("dotenv").config();

async function getChatGPTResponse(system_prompt, user_prompt, options = {}) {
  try {
    // Initialize OpenAI client with LiteLLM gateway
    const client = new OpenAI({
      apiKey: process.env.LITELLM_API_KEY || process.env.OPENAI_API_KEY,
      baseURL: 'https://ai.gateway.equalcollective.com/v1',
         model: process.env.OPENAI_MODEL_ID
    });

    const messages = [
      {
        role: "system",
        content: system_prompt,
      },
      {
        role: "user",
        content: user_prompt,
      },
    ];

    // Generate request body with direct tags or fallback to standard format
    let tags;
    if (options.directTags) {
      // Use direct tags for validation services
      tags = [...options.directTags];
      if (options.batchId) tags.push(`batchId:${options.batchId}`);
    } else {
      // Standard format for core services
      tags = ['sellerbot', 'scrapeGPT', 'getChatGPTResponse'];
      if (options.batchId) tags.push(`batchId:${options.batchId}`);
      if (options.customTags) tags.push(...options.customTags);
    }

    const requestBody = {
      model: options.model || process.env.OPENAI_MODEL_ID || 'gpt-4o',
      messages: messages,
      temperature: options.temperature || 1,
      presence_penalty: options.presence_penalty || 0,
      top_p: options.top_p || 1,
      max_tokens: options.max_tokens || 256,
      user: options.userId || options.user || 'system',
      metadata: {
        tags: tags
      }
    };

    const completion = await client.chat.completions.create(requestBody);

    result = completion.usage;
    result["message"] = completion.choices[0].message.content;
    result["prompt"] = messages;
    result["metadata"] = requestBody.metadata;
    return result;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    throw new Error("Error fetching response from ChatGPT: " + error);
  }
}

async function main() {
  try {
    const systemPrompt = "You are a helpful assistant.";
    const userPrompt = "What is the capital of France?";

    const response = await getChatGPTResponse(systemPrompt, userPrompt, {
      customTags: ['test:example']
    });
    console.log("ChatGPT Response:", response.message);
    console.log("Token Usage:", {
      promptTokens: response.prompt_tokens,
      completionTokens: response.completion_tokens,
      totalTokens: response.total_tokens,
    });
    console.log("Metadata Tags:", response.metadata.tags);
  } catch (error) {
    console.error("Error:", error.message);
  }
}

module.exports = {
  getChatGPTResponse,
};
