const { getChatGPTResponse } = require("./request");
const { getAssistantResponse } = require("./assistant");
const fs = require("fs");
async function getPromptTemplate(type) {
  // console.log(__dirname)
  switch (type) {
    case "match_text":
      return fs.readFileSync(`${__dirname}/matchText.md`, "utf8");
    case "match_url":
      return fs.readFileSync(`${__dirname}/matchUrl.md`, "utf8");
    case "match_image":
      return fs.readFileSync(`${__dirname}/matchImages.md`, "utf8");
  }
  return "Hello";
}

async function completionFactory(
  type,
  data,
  assistantId = null,
  stringify = true,
  options = {}
) {
  try {
    const system_prompt = await getPromptTemplate(type);
    const user_prompt = stringify ? JSON.stringify(data) : data;

    // Prepare tagging options for both assistant and chat completion
    const tagOptions = {
      batchId: options.batchId,
      customTags: [
        `type:${type}`,
        ...(options.customTags || [])
      ]
    };

    // console.log(user_prompt);
    const result = assistantId
      ? await getAssistantResponse(assistantId, user_prompt, tagOptions)
      : await getChatGPTResponse(system_prompt, user_prompt, tagOptions);
    return result;
  } catch (error) {
    console.error(`Error in ${type}:`, error.stack);
    throw new Error(`Error fetching text from ChatGPT: ${error}`);
  }
}

// Example usage
async function exampleUsage() {
  try {
    // const result = await completionFactory("match_url", {
    //   url: "https://tnpub.com/",
    //   businessKeywords: ["True North Publishing" , "The Starboard Group LLCn"],
    // }, null, true, { useCase: 'test', customTags: ['test:url_match'] });
    // console.log(result);

    const result = await completionFactory("match_text", {
      textContent: fs.readFileSync("services/scrapeGPT/data.txt", "utf8"),
      businessKeywords: ["True North Publishing", "The Starboard Group LLCn"],
    }, null, true, {
      customTags: ['test:text_match'],
      batchId: 'example_batch_001'
    });
    console.log(result);
    console.log("Metadata Tags:", result.metadata.tags);
  } catch (error) {
    console.error("Error:", error);
  }
}

// exampleUsage();
module.exports = { completionFactory };
