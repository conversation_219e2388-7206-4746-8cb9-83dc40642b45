# LiteLLM Integration Summary

This document summarizes the changes made to integrate LiteLLM as the base URL for all AI calls in SellerBot with standardized tagging format: `[sellerbot, service_name, function_name]`.

**CLEANED TAGGING**: Only approved tags are now used: validator:image, validator:htmlText, batchId. All other metadata tags have been removed.

## Files Modified

### 1. `utils/aiMetadataHelper.js` (Created)
- **Purpose**: Centralized utility for generating standardized metadata and tags
- **Key Features**:
  - Standardized tagging format: `[sellerbot, service_name, function_name]`
  - Additional metadata tags in `key:value` format
  - Service-specific generators for consistent tagging
  - LiteLLM-compatible request body generation

### 2. `config/litellmConfig.js` (Created)
- **Purpose**: Centralized configuration for LiteLLM settings
- **Key Features**:
  - Default models and fallback configuration
  - Service-specific configurations
  - Environment variable management
  - Configuration validation utilities

### 3. `services/scrapeGPT/request.js` (Updated)
- **Changes**:
  - Replaced `AzureOpenAI` with standard `OpenAI` client
  - Updated to use LiteLLM gateway URL: `https://ai.gateway.equalcollective.com/v1`
  - Added standardized tagging: `[sellerbot, scrapeGPT, getChatGPTResponse]`
  - Added support for options parameter to pass custom tags and metadata
  - Enhanced error handling and result structure

### 4. `services/scrapeGPT/assistant.js` (Updated)
- **Changes**:
  - Updated OpenAI client to use LiteLLM gateway
  - Added standardized tagging: `[sellerbot, assistant, getAssistantResponse]`
  - Added metadata to thread creation
  - Enhanced result structure with tagging information
  - Added support for options parameter

### 5. `services/scrapeGPT/factory.js` (Updated)
- **Changes**:
  - Added options parameter to `completionFactory` function
  - Implemented tagging for both assistant and chat completion calls
  - Added factory-specific tags like `factoryType` and `hasAssistant`
  - Enhanced example usage with tagging demonstration

### 6. `services/generateLeads/validaterMethods/validateHtmlText.js` (Updated)
- **Changes**:
  - Updated `completionFactory` call to include tagging options
  - Added validator-specific tags: `validator:htmlText`
  - Added contextual tags for keyword count and text length

### 7. `services/generateLeads/validaterMethods/validateImage.js` (Updated)
- **Changes**:
  - Updated `completionFactory` call to include tagging options
  - Added validator-specific tags: `validator:image`
  - Added contextual tags for image validation

### 8. `test_litellm_integration.js` (Created)
- **Purpose**: Test script to verify LiteLLM integration
- **Features**:
  - Tests all updated functions
  - Validates tagging format
  - Provides comprehensive test results
  - Includes troubleshooting guidance

## Tagging Format

### Core Tags (First 3 elements)
```javascript
[
  'sellerbot',           // Always first
  'service_name',        // e.g., 'scrapeGPT', 'assistant', 'leadGeneration'
  'function_name'        // e.g., 'getChatGPTResponse', 'getAssistantResponse'
]
```

### Additional Metadata Tags (key:value format)
```javascript
[
  'requestId:req_1234567890_abc123',
  'sessionId:sellerbot_1234567890_def456',
  'file:request.js',
  'environment:development',
  'timestamp:2024-01-15T10:30:00.000Z',
  'useCase:scrape_analysis',
  'operationType:scrape_gpt',
  'feature:content_analysis',
  'domain:example.com',
  'batchId:batch_001',
  // ... custom tags
]
```

## Environment Variables Required

```bash
# Primary LiteLLM configuration
LITELLM_API_KEY=sk-your-litellm-key-here
LITELLM_PROXY_URL=https://ai.gateway.equalcollective.com

# Fallback (if LITELLM_API_KEY not set)
OPENAI_API_KEY=sk-your-openai-key-here

# Optional: Enable metadata logging
AI_METADATA_LOGGING=true
LOG_LEVEL=info
NODE_ENV=development
```

## Usage Examples

### Basic Chat Completion
```javascript
const { getChatGPTResponse } = require('./services/scrapeGPT/request');

const result = await getChatGPTResponse(
  "You are a helpful assistant.",
  "Analyze this content...",
  {
    useCase: 'content_analysis',
    domain: 'example.com',
    customTags: ['priority:high', 'batch:001']
  }
);
```

### Assistant API
```javascript
const { getAssistantResponse } = require('./services/scrapeGPT/assistant');

const result = await getAssistantResponse(
  'asst_123456',
  JSON.stringify(data),
  {
    useCase: 'structured_analysis',
    customTags: ['assistant:content_analyzer']
  }
);
```

### Completion Factory
```javascript
const { completionFactory } = require('./services/scrapeGPT/factory');

const result = await completionFactory(
  'match_text',
  { textContent: '...', businessKeywords: [...] },
  null, // no assistant
  true, // stringify
  {
    useCase: 'text_matching',
    batchId: 'validation_batch_001',
    customTags: ['validator:htmlText']
  }
);
```

## Testing

Run the integration test:
```bash
node test_litellm_integration.js
```

This will verify:
- ✅ LiteLLM connectivity
- ✅ Standardized tagging format
- ✅ All updated functions work correctly
- ✅ Metadata is properly attached

## Benefits

1. **Centralized AI Gateway**: All AI calls go through LiteLLM for unified monitoring
2. **Standardized Tagging**: Consistent format across all services for better tracking
3. **Cost Tracking**: Detailed usage analytics with business context
4. **Fallback Support**: Automatic model fallback and error handling
5. **Observability**: Rich metadata for debugging and optimization
6. **Scalability**: Easy to add new AI services with consistent patterns

## Next Steps

1. **Monitor Usage**: Check LiteLLM dashboard for request analytics
2. **Optimize Models**: Use usage data to optimize model selection
3. **Add More Services**: Migrate remaining AI services using the same pattern
4. **Cost Analysis**: Set up budget alerts and cost tracking
5. **Performance Tuning**: Monitor latency and adjust configurations

## Troubleshooting

If you encounter issues:

1. **Check Environment Variables**: Ensure `LITELLM_API_KEY` is set
2. **Test Connectivity**: Verify access to `https://ai.gateway.equalcollective.com`
3. **Run Tests**: Use `test_litellm_integration.js` to diagnose issues
4. **Check Logs**: Enable `AI_METADATA_LOGGING=true` for detailed logs
5. **Validate Tags**: Ensure tagging format is correct in LiteLLM dashboard
