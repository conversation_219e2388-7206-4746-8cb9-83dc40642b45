/**
 * Step 1: Fetch SP data from Query 271 and create lookup table
 * Run: node analytics_v2_with_sp/1_fetchSPData.js
 */

require("dotenv").config({ path: "../.env" });
const axios = require('axios');
const prisma = require("../database/prisma/getPrismaClient");

const METABASE_URL = 'https://metabase.equalcollective.com';
const API_KEY = 'mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00=';

async function fetchQuery271SPData() {
  console.log('📥 Fetching SP data from Query 271...');
  
  try {
    const response = await axios.post(
      `${METABASE_URL}/api/card/271/query/json`,
      {},
      {
        headers: { 'X-API-KEY': API_KEY },
        timeout: 300000 // 5 minutes
      }
    );
    
    const data = response.data;
    console.log(`✅ Fetched ${data.length} prospect records`);
    
    // Extract email->SP mappings
    const spMappings = [];
    data.forEach(record => {
      const email = record['Prospect → Email'];
      const sp = record.dominant_search_priority || 'UNK';
      
      if (email) {
        spMappings.push({
          email: email.toLowerCase().trim(),
          sp: sp,
          sellerId: record.amazon_seller_id,
          businessName: record.business_name
        });
      }
    });
    
    const withSP = spMappings.filter(m => m.sp !== 'UNK').length;
    console.log(`   With SP: ${withSP} (${(withSP/spMappings.length*100).toFixed(1)}%)`);
    console.log(`   Without SP: ${spMappings.length - withSP} (${((spMappings.length - withSP)/spMappings.length*100).toFixed(1)}%)`);
    
    return spMappings;
  } catch (error) {
    console.error('❌ Error fetching Query 271:', error.message);
    throw error;
  }
}

async function createSPLookupTable(spMappings) {
  console.log('\n💾 Creating SP lookup table...');
  
  try {
    // Drop and recreate table
    await prisma.$executeRawUnsafe(`
      DROP TABLE IF EXISTS sp_email_lookup;
      
      CREATE TABLE sp_email_lookup (
        email VARCHAR(255) PRIMARY KEY,
        sp VARCHAR(10) NOT NULL DEFAULT 'UNK',
        seller_id VARCHAR(100),
        business_name VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    console.log('   Table created: sp_email_lookup');
    
    // Batch insert
    const batchSize = 1000;
    let inserted = 0;
    
    for (let i = 0; i < spMappings.length; i += batchSize) {
      const batch = spMappings.slice(i, i + batchSize);
      
      for (const mapping of batch) {
        await prisma.$executeRawUnsafe(`
          INSERT INTO sp_email_lookup (email, sp, seller_id, business_name) 
          VALUES ($1, $2, $3, $4)
          ON CONFLICT (email) DO UPDATE SET sp = EXCLUDED.sp
        `, mapping.email, mapping.sp, mapping.sellerId, mapping.businessName);
        inserted++;
      }
      
      if (inserted % 10000 === 0) {
        console.log(`   Inserted ${inserted}/${spMappings.length}...`);
      }
    }
    
    console.log(`✅ Loaded ${inserted} SP mappings`);
    
    // Create index
    await prisma.$executeRawUnsafe('CREATE INDEX idx_sp_lookup_sp ON sp_email_lookup(sp);');
    
    // Show distribution
    const stats = await prisma.$queryRawUnsafe(`
      SELECT sp, COUNT(*) as count
      FROM sp_email_lookup
      GROUP BY sp
      ORDER BY sp
    `);
    
    console.log('\n📊 SP Distribution:');
    stats.forEach(row => {
      console.log(`   ${row.sp}: ${row.count}`);
    });
    
  } catch (error) {
    console.error('❌ Error creating lookup table:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🚀 Step 1: Setting up SP Data');
  console.log('=' .repeat(60));
  
  try {
    const spMappings = await fetchQuery271SPData();
    await createSPLookupTable(spMappings);
    
    console.log('\n' + '=' .repeat(60));
    console.log('✅ SP lookup table ready');
    console.log('   Next: Run 2_createMV.js');
  } catch (error) {
    console.error('Failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();