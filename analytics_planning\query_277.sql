SELECT
  mv_email."id" AS "id",
  mv_email."leadId" AS "leadId",
  mv_email."subject" AS "subject",
  mv_email."body" AS "body",
  mv_email."type" AS "type",
  "Prospects - email_type"."Seller Group - Prospects with eMail Classification__626da005" AS "prospect_email_type",
  "Prospects - email_type".jeff_search_priority,
  "Prospects - email_type".dominant_search_priority,
  mv_email."toEmailID" AS "toEmailID",
  mv_email."fromEmailID" AS "fromEmailID",
  mv_email."time" AS "time",
  mv_email."messageId" AS "messageId",
  mv_email."campaingId" AS "campaingId",
  mv_email."email_seq_number" AS "email_seq_number",
  mv_email."open_count" AS "open_count",
  mv_email."threadStartEmailId" AS "threadStartEmailId",
  mv_email."threadToEmailId" AS "threadToEmailId",
  "Campaign - campaingId"."name" AS "Campaign - campaingId__name",
  "Campaign - campaingId"."parentCampaignId" AS "parentCampaignId",
  "Client - clientId"."name" AS "jeff_client_person",
  "Client - clientId"."clientId" AS "clientSmartLeadId",
  "Client - clientId"."businessName" AS "jeff_client_name",
  CASE 
    WHEN SUBSTRING("Campaign - campaingId"."name" FROM '[0-9]{1,2}[A-Z]') IS NOT NULL 
      THEN SUBSTRING("Campaign - campaingId"."name" FROM '[0-9]{1,2}[A-Z]')
    WHEN "Campaign - campaingId"."parentCampaignId" IS NOT NULL 
      THEN 'HKP'
    ELSE 'UNK'
  END AS "jeff_campaign_code",
  CASE
    WHEN mv_email."type" IN ('FORWARD', 'SENT') THEN mv_email."type"
    WHEN mv_email."type" = 'REPLY' THEN CASE
      WHEN LOWER(mv_email."messageId") LIKE '%zendesk%'
      OR LOWER(mv_email."messageId") LIKE '%freshdesk%'
      OR LOWER(mv_email."messageId") LIKE '%helpscout%'
      OR LOWER(mv_email."messageId") LIKE '%hubspot%'
      OR LOWER(mv_email."messageId") LIKE '%intercom%'
      OR LOWER(mv_email."body") LIKE '%is an automated message%'
      OR LOWER(mv_email."body") LIKE '%important to us%'
      OR LOWER(mv_email."body") LIKE '%will get back to you as quickly as possible%'
      OR LOWER(mv_email."body") LIKE '%customer service%'
      OR LOWER(mv_email."body") LIKE '%auto message%'
      OR LOWER(mv_email."body") LIKE '%customer care%'
      OR LOWER(mv_email."body") LIKE '%will get back to you within%'
      OR LOWER(mv_email."body") LIKE '%auto-reply%'
      OR LOWER(mv_email."body") LIKE '%for your patience%'
      OR LOWER(mv_email."body") LIKE '%thank you for your request%'
      OR LOWER(mv_email."body") LIKE '%business hours are%'
      OR LOWER(mv_email."body") LIKE '%received your request.%'
      OR LOWER(mv_email."body") LIKE '%hear back from us within%'
      OR LOWER(mv_email."body") LIKE '%thank you for contacting%'
      OR LOWER(mv_email."body") LIKE '%thank you for reaching%'
      OR LOWER(mv_email."body") LIKE '%support experience%'
      OR LOWER(mv_email."body") LIKE '%support team%'
      OR LOWER(mv_email."body") LIKE '%**is an automated message**%' THEN 'REPLY_CS_AUTOMATED'
      WHEN LOWER(mv_email."messageId") LIKE '%mx.google.com%' THEN 'ERROR_REPLY'
      WHEN (
        LOWER(mv_email."messageId") LIKE '%prod.outlook.com%'
        OR LOWER(mv_email."messageId") LIKE '%exchangelabs.com%'
      )
      AND (
        LOWER(mv_email."body") LIKE '%fail%'
        OR LOWER(mv_email."body") LIKE '%failure%'
        OR LOWER(mv_email."body") LIKE '%error%'
        OR LOWER(mv_email."body") LIKE '%action required recipient unknown%'
        OR LOWER(mv_email."body") LIKE '%your message to%'
        OR LOWER(mv_email."body") LIKE '%rejected%'
      ) THEN 'ERROR_REPLY'
      WHEN LOWER(mv_email."body") LIKE '%automatically by mail delivery software%'
      OR LOWER(mv_email."body") LIKE '%delivery has failed%'
      OR LOWER(mv_email."body") LIKE '%created automatically by mail delivery software%'
      OR LOWER(mv_email."body") LIKE '%your message could not be delivered%'
      OR LOWER(mv_email."body") LIKE '%message could not be delivered%'
      OR LOWER(mv_email."body") LIKE '%recipient address rejected%'
      OR (
        LOWER(mv_email."body") LIKE '%mail system%'
        AND LOWER(mv_email."body") LIKE '%rejected%'
      ) THEN 'ERROR_REPLY'
      ELSE 'REPLY'
    END
    ELSE NULL
  END AS "jeff_email_status"
FROM
  mv_email_with_thread_start mv_email
  LEFT JOIN "public"."Campaign" AS "Campaign - campaingId" ON mv_email."campaingId" = "Campaign - campaingId"."campaignId"
  LEFT JOIN "public"."Client" AS "Client - clientId" ON "Campaign - campaingId"."clientId" = "Client - clientId"."clientId"
  LEFT JOIN {{#271-seller-group-sellers-with-sp-classifications-prospects-w-emails}} AS "Prospects - email_type" 
    ON mv_email."toEmailID" = "Prospects - email_type"."Seller Group - Prospects with eMail Classification__email"
LIMIT 1048575;