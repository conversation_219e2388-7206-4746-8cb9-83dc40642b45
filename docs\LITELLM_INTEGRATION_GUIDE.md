# LiteLLM Gateway Integration Guide

Complete guide for integrating with the LiteLLM gateway using OpenAI SDK, curl requests, and third-party services.

## Table of Contents

1. [Overview](#overview)
2. [Getting Started](#getting-started)
3. [API Key Management](#api-key-management)
4. [Integration Methods](#integration-methods)
5. [Tagging and Metadata](#tagging-and-metadata)
6. [Examples](#examples)
7. [LangGraph Integration](#langgraph-integration)
8. [Third-Party Service Integration](#third-party-service-integration)
9. [Testing](#testing)
10. [Troubleshooting](#troubleshooting)

## Overview

LiteLLM provides a unified interface to multiple LLM providers through a proxy gateway. This allows you to use any LLM provider with the same OpenAI-compatible API.

**Gateway URL**: `https://ai.gateway.equalcollective.com/`

### Key Benefits

- **Unified API**: Use OpenAI SDK with any LLM provider
- **Cost Tracking**: Built-in usage and cost monitoring with tags
- **Load Balancing**: Automatic routing across multiple providers
- **Fallback Support**: Automatic failover between providers
- **Rate Limiting**: Built-in rate limiting and quota management

### Supported Integration Methods

- **OpenAI SDK** (JavaScript/Python) - Drop-in replacement
- **Direct HTTP/curl requests** - REST API calls
- **LangGraph wrapper** - For complex AI workflows
- **Third-party services** - Integrate any service that supports OpenAI API

## Getting Started

### Prerequisites

1. Access to the LiteLLM gateway
2. Valid API key for your persona/use case
3. OpenAI SDK installed (if using SDK approach)

### Installation

```bash
# For JavaScript/Node.js
npm install openai

# For Python
pip install openai
```

## API Key Management

### Creating Keys for Persona Use

1. **Contact your administrator** to get a persona-specific API key
2. **Key format**: Keys should start with `sk-` followed by your persona identifier
3. **Environment setup**: Store your key securely in environment variables

```bash
# .env file
LITELLM_API_KEY=sk-your-persona-key-here
LITELLM_PROXY_URL=https://ai.gateway.equalcollective.com
```

### Key Security Best Practices

- Never commit API keys to version control
- Use environment variables or secure key management systems
- Rotate keys regularly
- Use different keys for different environments (dev/staging/prod)

## Integration Methods

### 1. OpenAI SDK Integration (JavaScript)

#### Basic Setup

```javascript
const OpenAI = require('openai');

// Initialize client with LiteLLM gateway
const client = new OpenAI({
    apiKey: process.env.LITELLM_API_KEY,
    baseURL: 'https://ai.gateway.equalcollective.com/v1'
});

// Basic chat completion
async function basicChat() {
    const completion = await client.chat.completions.create({
        model: 'gpt-4o',
        messages: [
            { role: 'system', content: 'You are a helpful assistant.' },
            { role: 'user', content: 'Hello, how are you?' }
        ],
        max_tokens: 100,
        temperature: 0.7
    });

    return completion.choices[0].message.content;
}
```

#### Streaming Responses

```javascript
async function streamingChat() {
    const stream = await client.chat.completions.create({
        model: 'gpt-4o',
        messages: [
            { role: 'user', content: 'Count from 1 to 10' }
        ],
        stream: true
    });

    let fullResponse = '';
    for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        fullResponse += content;
        process.stdout.write(content);
    }

    return fullResponse;
}
```

#### Multiple Models Support

```javascript
async function testMultipleModels() {
    const models = ['gpt-4o', 'gpt-3.5-turbo', 'claude-3-sonnet-20240229'];

    for (const model of models) {
        try {
            const completion = await client.chat.completions.create({
                model: model,
                messages: [
                    { role: 'user', content: `Say hello from ${model}` }
                ],
                max_tokens: 50
            });

            console.log(`${model}: ${completion.choices[0].message.content}`);
        } catch (error) {
            console.log(`${model}: Failed - ${error.message}`);
        }
    }
}
```

### 2. Direct HTTP/curl Requests

#### Basic curl Example

```bash
curl -X POST "https://ai.gateway.equalcollective.com/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $LITELLM_API_KEY" \
  -d '{
    "model": "gpt-4o",
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user", 
        "content": "Hello, how are you?"
      }
    ],
    "max_tokens": 100,
    "temperature": 0.7
  }'
```

#### JavaScript fetch Example

```javascript
async function directHttpRequest() {
    const response = await fetch('https://ai.gateway.equalcollective.com/v1/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.LITELLM_API_KEY}`
        },
        body: JSON.stringify({
            model: 'gpt-4o',
            messages: [
                { role: 'system', content: 'You are a helpful assistant.' },
                { role: 'user', content: 'Hello, how are you?' }
            ],
            max_tokens: 100,
            temperature: 0.7
        })
    });
    
    const data = await response.json();
    return data.choices[0].message.content;
}
```

## Tagging and Metadata

### Why Use Tags?

Tags help with:
- **Cost tracking** by project/feature
- **Usage analytics** and monitoring
- **Debugging** and troubleshooting
- **Performance optimization**

### Tag Format

LiteLLM uses a specific tag format: `["key:value", "key:value"]`

### Basic Tagging Example

```javascript
const requestWithTags = {
    model: 'gpt-4o',
    messages: [
        { role: 'user', content: 'What is the weather like?' }
    ],
    user: 'sellerbot_user', // Optional: track spend by user
    metadata: {
        tags: [
            'jobID:12345',
            'taskName:weather_query',
            'service:SellerBot',
            'function:getWeather',
            'useCase:customer_support',
            'environment:production',
            'requestType:chat_completion'
        ]
    }
};
```

### Advanced Tagging with curl

```bash
curl -X POST "https://ai.gateway.equalcollective.com/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $LITELLM_API_KEY" \
  -d '{
    "model": "gpt-4o",
    "messages": [
      {"role": "user", "content": "Analyze this data"}
    ],
    "user": "data_analyst_01",
    "metadata": {
      "tags": [
        "jobID:analytics_2024_001",
        "taskName:data_analysis", 
        "service:DataPlatform",
        "function:analyzeDataset",
        "useCase:business_intelligence",
        "environment:production",
        "priority:high",
        "department:analytics"
      ]
    }
  }'
```

## Examples

### OpenAI SDK with Tags

#### Complete JavaScript Service Class

```javascript
const OpenAI = require('openai');

class LiteLLMService {
    constructor() {
        this.client = new OpenAI({
            apiKey: process.env.LITELLM_API_KEY,
            baseURL: 'https://ai.gateway.equalcollective.com/v1'
        });
    }

    async chatWithTags(messages, options = {}) {
        const requestBody = {
            model: options.model || 'gpt-4o',
            messages: messages,
            max_tokens: options.maxTokens || 1000,
            temperature: options.temperature || 0.7,
            user: options.user || 'default_user',
            metadata: {
                tags: [
                    `requestId:${this.generateRequestId()}`,
                    `service:${options.service || 'SellerBot'}`,
                    `function:${options.functionName || 'chatWithTags'}`,
                    `useCase:${options.useCase || 'general'}`,
                    `environment:${process.env.NODE_ENV || 'development'}`,
                    ...options.customTags || []
                ]
            }
        };

        try {
            const completion = await this.client.chat.completions.create(requestBody);
            return {
                success: true,
                message: completion.choices[0].message.content,
                usage: completion.usage,
                metadata: requestBody.metadata
            };
        } catch (error) {
            console.error('LiteLLM request failed:', error);
            throw error;
        }
    }

    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

// Usage example
const litellm = new LiteLLMService();

async function analyzeSellerData() {
    const result = await litellm.chatWithTags([
        { role: 'system', content: 'You are an Amazon seller data analyst.' },
        { role: 'user', content: 'Analyze this seller performance data...' }
    ], {
        service: 'SellerBot',
        functionName: 'analyzeSellerData',
        useCase: 'seller_analytics',
        user: 'analyst_user_123',
        customTags: [
            'jobID:seller_analysis_001',
            'taskName:performance_analysis',
            'priority:high'
        ]
    });

    console.log('Analysis result:', result.message);
    console.log('Token usage:', result.usage);
}
```

### Without Tags (Simple Usage)

#### JavaScript - Basic Usage

```javascript
const OpenAI = require('openai');

const client = new OpenAI({
    apiKey: process.env.LITELLM_API_KEY,
    baseURL: 'https://ai.gateway.equalcollective.com/v1'
});

async function simpleChat() {
    const completion = await client.chat.completions.create({
        model: 'gpt-4o',
        messages: [
            { role: 'user', content: 'What is 2+2?' }
        ]
    });

    return completion.choices[0].message.content;
}
```

#### curl - Basic Usage

```bash
curl -X POST "https://ai.gateway.equalcollective.com/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $LITELLM_API_KEY" \
  -d '{
    "model": "gpt-4o",
    "messages": [
      {"role": "user", "content": "What is 2+2?"}
    ]
  }'
```

## LangGraph Integration

LangGraph can be integrated with LiteLLM by configuring it to use the gateway as the OpenAI endpoint.

### Basic LangGraph Setup

```javascript
const { ChatOpenAI } = require("@langchain/openai");
const { StateGraph } = require("@langchain/langgraph");

// Configure LangGraph to use LiteLLM gateway
const llm = new ChatOpenAI({
    openAIApiKey: process.env.LITELLM_API_KEY,
    configuration: {
        baseURL: "https://ai.gateway.equalcollective.com/v1"
    },
    modelName: "gpt-4o"
});

// Create a simple workflow
const workflow = new StateGraph({
    channels: {
        messages: {
            value: (x, y) => x.concat(y),
            default: () => []
        }
    }
});

workflow.addNode("agent", async (state) => {
    const response = await llm.invoke(state.messages);
    return { messages: [response] };
});

workflow.setEntryPoint("agent");
workflow.setFinishPoint("agent");

const app = workflow.compile();
```

### LangGraph with Tags

```javascript
const { ChatOpenAI } = require("@langchain/openai");

class TaggedLangGraphLLM extends ChatOpenAI {
    constructor(options = {}) {
        super({
            openAIApiKey: process.env.LITELLM_API_KEY,
            configuration: {
                baseURL: "https://ai.gateway.equalcollective.com/v1"
            },
            modelName: options.model || "gpt-4o",
            ...options
        });

        this.tags = options.tags || [];
    }

    async _generate(messages, options) {
        // Add metadata tags to the request
        const enhancedOptions = {
            ...options,
            user: options.user || 'langgraph_user',
            metadata: {
                tags: [
                    'framework:langgraph',
                    'service:SellerBot',
                    `workflow:${options.workflowName || 'default'}`,
                    `step:${options.stepName || 'unknown'}`,
                    ...this.tags,
                    ...options.customTags || []
                ]
            }
        };

        return super._generate(messages, enhancedOptions);
    }
}

// Usage in workflow
const taggedLLM = new TaggedLangGraphLLM({
    model: "gpt-4o",
    tags: [
        'useCase:seller_analysis',
        'priority:high'
    ]
});
```

## Third-Party Service Integration

### Integrating with External APIs

Many third-party services support OpenAI-compatible APIs. Here's how to configure them to use LiteLLM:

#### Generic Service Configuration

```javascript
// For any service that accepts OpenAI configuration
const serviceConfig = {
    apiKey: process.env.LITELLM_API_KEY,
    baseURL: 'https://ai.gateway.equalcollective.com/v1',
    model: 'gpt-4o'
};

// Example: Configuring a hypothetical AI service
const aiService = new ThirdPartyAIService(serviceConfig);
```

#### Custom Wrapper for Third-Party Services

```javascript
class LiteLLMWrapper {
    constructor(originalService) {
        this.originalService = originalService;
        this.litellmClient = new OpenAI({
            apiKey: process.env.LITELLM_API_KEY,
            baseURL: 'https://ai.gateway.equalcollective.com/v1'
        });
    }

    async processWithLiteLLM(input, options = {}) {
        // Convert third-party input to OpenAI format
        const messages = this.convertToOpenAIFormat(input);

        // Add service-specific tags
        const requestBody = {
            model: options.model || 'gpt-4o',
            messages: messages,
            metadata: {
                tags: [
                    `thirdPartyService:${this.originalService.name}`,
                    `integration:wrapper`,
                    `inputType:${typeof input}`,
                    ...options.tags || []
                ]
            }
        };

        const completion = await this.litellmClient.chat.completions.create(requestBody);

        // Convert back to third-party format
        return this.convertFromOpenAIFormat(completion);
    }

    convertToOpenAIFormat(input) {
        // Implement conversion logic based on third-party format
        if (typeof input === 'string') {
            return [{ role: 'user', content: input }];
        }
        return input;
    }

    convertFromOpenAIFormat(completion) {
        // Convert OpenAI response to third-party expected format
        return completion.choices[0].message.content;
    }
}
```

## Testing

### Basic Connection Test

```javascript
const OpenAI = require('openai');

async function testLiteLLMConnection() {
    const client = new OpenAI({
        apiKey: process.env.LITELLM_API_KEY,
        baseURL: 'https://ai.gateway.equalcollective.com/v1'
    });

    try {
        // Test health endpoint
        console.log('Testing gateway health...');
        const healthResponse = await fetch('https://ai.gateway.equalcollective.com/health');
        console.log('Health status:', healthResponse.status);

        // Test models endpoint
        console.log('Testing models endpoint...');
        const models = await client.models.list();
        console.log('Available models:', models.data.slice(0, 3).map(m => m.id));

        // Test chat completion
        console.log('Testing chat completion...');
        const completion = await client.chat.completions.create({
            model: 'gpt-4o',
            messages: [
                { role: 'user', content: 'Say "LiteLLM test successful!"' }
            ],
            max_tokens: 50
        });

        console.log('Response:', completion.choices[0].message.content);
        console.log('✅ All tests passed!');

        return true;
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        return false;
    }
}

// Run the test
testLiteLLMConnection();
```

### Testing with Tags

```javascript
async function testWithTags() {
    const client = new OpenAI({
        apiKey: process.env.LITELLM_API_KEY,
        baseURL: 'https://ai.gateway.equalcollective.com/v1'
    });

    const requestWithTags = {
        model: 'gpt-4o',
        messages: [
            { role: 'user', content: 'Test message with tags' }
        ],
        user: 'test_user',
        metadata: {
            tags: [
                'test:connection',
                'environment:testing',
                'service:documentation',
                'function:testWithTags'
            ]
        }
    };

    try {
        const completion = await client.chat.completions.create(requestWithTags);
        console.log('✅ Tagged request successful');
        console.log('Response:', completion.choices[0].message.content);
        console.log('Usage:', completion.usage);
        return true;
    } catch (error) {
        console.error('❌ Tagged request failed:', error.message);
        return false;
    }
}
```

### Comprehensive Test Suite

```javascript
class LiteLLMTester {
    constructor() {
        this.client = new OpenAI({
            apiKey: process.env.LITELLM_API_KEY,
            baseURL: 'https://ai.gateway.equalcollective.com/v1'
        });
        this.results = {};
    }

    async runAllTests() {
        console.log('🚀 Starting LiteLLM Gateway Tests\n');

        this.results.health = await this.testHealth();
        this.results.models = await this.testModels();
        this.results.basicChat = await this.testBasicChat();
        this.results.streaming = await this.testStreaming();
        this.results.tags = await this.testTags();
        this.results.multipleModels = await this.testMultipleModels();

        this.printSummary();
        return this.results;
    }

    async testHealth() {
        try {
            const response = await fetch('https://ai.gateway.equalcollective.com/health');
            const success = response.status === 200;
            console.log(`${success ? '✅' : '❌'} Health Check: ${response.status}`);
            return success;
        } catch (error) {
            console.log(`❌ Health Check: ${error.message}`);
            return false;
        }
    }

    async testModels() {
        try {
            const models = await this.client.models.list();
            const success = models.data && models.data.length > 0;
            console.log(`${success ? '✅' : '❌'} Models: Found ${models.data?.length || 0} models`);
            return success;
        } catch (error) {
            console.log(`❌ Models: ${error.message}`);
            return false;
        }
    }

    async testBasicChat() {
        try {
            const completion = await this.client.chat.completions.create({
                model: 'gpt-4o',
                messages: [{ role: 'user', content: 'Say hello' }],
                max_tokens: 10
            });

            const success = completion.choices?.[0]?.message?.content;
            console.log(`${success ? '✅' : '❌'} Basic Chat: ${success ? 'Success' : 'Failed'}`);
            return !!success;
        } catch (error) {
            console.log(`❌ Basic Chat: ${error.message}`);
            return false;
        }
    }

    async testStreaming() {
        try {
            const stream = await this.client.chat.completions.create({
                model: 'gpt-4o',
                messages: [{ role: 'user', content: 'Count 1, 2, 3' }],
                stream: true,
                max_tokens: 20
            });

            let chunks = 0;
            for await (const chunk of stream) {
                if (chunk.choices?.[0]?.delta?.content) chunks++;
            }

            const success = chunks > 0;
            console.log(`${success ? '✅' : '❌'} Streaming: ${chunks} chunks received`);
            return success;
        } catch (error) {
            console.log(`❌ Streaming: ${error.message}`);
            return false;
        }
    }

    async testTags() {
        try {
            const completion = await this.client.chat.completions.create({
                model: 'gpt-4o',
                messages: [{ role: 'user', content: 'Test with tags' }],
                user: 'test_user',
                metadata: {
                    tags: ['test:tags', 'environment:testing']
                },
                max_tokens: 10
            });

            const success = completion.choices?.[0]?.message?.content;
            console.log(`${success ? '✅' : '❌'} Tags: ${success ? 'Success' : 'Failed'}`);
            return !!success;
        } catch (error) {
            console.log(`❌ Tags: ${error.message}`);
            return false;
        }
    }

    async testMultipleModels() {
        const models = ['gpt-4o', 'gpt-3.5-turbo'];
        let successCount = 0;

        for (const model of models) {
            try {
                const completion = await this.client.chat.completions.create({
                    model: model,
                    messages: [{ role: 'user', content: 'Hi' }],
                    max_tokens: 5
                });

                if (completion.choices?.[0]?.message?.content) {
                    successCount++;
                }
            } catch (error) {
                // Model might not be available
            }
        }

        const success = successCount > 0;
        console.log(`${success ? '✅' : '❌'} Multiple Models: ${successCount}/${models.length} working`);
        return success;
    }

    printSummary() {
        const total = Object.keys(this.results).length;
        const passed = Object.values(this.results).filter(Boolean).length;

        console.log('\n📊 Test Summary:');
        console.log(`Total: ${total}, Passed: ${passed}, Failed: ${total - passed}`);

        if (passed === total) {
            console.log('🎉 All tests passed! LiteLLM gateway is working perfectly.');
        } else {
            console.log('⚠️ Some tests failed. Check your configuration.');
        }
    }
}

// Usage
const tester = new LiteLLMTester();
tester.runAllTests();
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Authentication Errors

**Error**: `401 Unauthorized` or `Invalid API key`

**Solutions**:
```javascript
// Check if API key is properly set
console.log('API Key:', process.env.LITELLM_API_KEY ? 'Set' : 'Missing');

// Verify key format (should start with 'sk-')
if (!process.env.LITELLM_API_KEY?.startsWith('sk-')) {
    console.error('API key should start with "sk-"');
}

// Test with a simple request
const testAuth = async () => {
    try {
        const response = await fetch('https://ai.gateway.equalcollective.com/v1/models', {
            headers: {
                'Authorization': `Bearer ${process.env.LITELLM_API_KEY}`
            }
        });
        console.log('Auth test status:', response.status);
    } catch (error) {
        console.error('Auth test failed:', error.message);
    }
};
```

#### 2. Connection Issues

**Error**: `ECONNREFUSED` or `Network timeout`

**Solutions**:
```javascript
// Test gateway connectivity
const testConnection = async () => {
    try {
        const response = await fetch('https://ai.gateway.equalcollective.com/health/liveliness', {
            timeout: 5000
        });
        console.log('Gateway reachable:', response.status === 200);
    } catch (error) {
        console.error('Gateway unreachable:', error.message);
        // Check if you're behind a firewall or proxy
    }
};

// Use with timeout and retry logic
const clientWithRetry = new OpenAI({
    apiKey: process.env.LITELLM_API_KEY,
    baseURL: 'https://ai.gateway.equalcollective.com/v1',
    timeout: 30000, // 30 second timeout
    maxRetries: 3
});
```

#### 2a. Parameter Validation Errors

**Error**: `BadRequestError - Invalid 'max_tokens': integer below minimum value`

**Solutions**:
```javascript
// Ensure proper parameter validation
const safeRequest = async (messages, options = {}) => {
    const requestBody = {
        model: options.model || 'gpt-4o',
        messages: messages,
        max_tokens: Math.max(options.maxTokens || 1000, 1), // Ensure >= 1
        temperature: Math.max(0, Math.min(options.temperature || 0.7, 2)), // 0-2 range
        user: options.user || 'default_user',
        metadata: options.metadata || { tags: [] }
    };

    // Validate parameters before sending
    if (requestBody.max_tokens < 1) {
        throw new Error('max_tokens must be >= 1');
    }

    return await client.chat.completions.create(requestBody);
};
```

#### 3. Model Not Available

**Error**: `Model not found` or `Invalid model`

**Solutions**:
```javascript
// Check available models
const checkModels = async () => {
    const client = new OpenAI({
        apiKey: process.env.LITELLM_API_KEY,
        baseURL: 'https://ai.gateway.equalcollective.com/v1'
    });

    try {
        const models = await client.models.list();
        console.log('Available models:');
        models.data.forEach(model => console.log(`- ${model.id}`));
    } catch (error) {
        console.error('Failed to fetch models:', error.message);
    }
};

// Use fallback models
const requestWithFallback = async (messages) => {
    const models = ['gpt-4o', 'gpt-3.5-turbo', 'claude-3-sonnet-20240229'];

    for (const model of models) {
        try {
            return await client.chat.completions.create({
                model: model,
                messages: messages
            });
        } catch (error) {
            console.log(`Model ${model} failed, trying next...`);
        }
    }

    throw new Error('All models failed');
};
```

#### 4. Rate Limiting

**Error**: `429 Too Many Requests`

**Solutions**:
```javascript
// Implement exponential backoff
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const requestWithBackoff = async (requestFn, maxRetries = 3) => {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await requestFn();
        } catch (error) {
            if (error.status === 429 && i < maxRetries - 1) {
                const delay = Math.pow(2, i) * 1000; // Exponential backoff
                console.log(`Rate limited, waiting ${delay}ms...`);
                await sleep(delay);
                continue;
            }
            throw error;
        }
    }
};

// Usage
const result = await requestWithBackoff(() =>
    client.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: 'Hello' }]
    })
);
```

#### 5. Invalid Response Format

**Error**: Response doesn't match expected format

**Solutions**:
```javascript
// Validate response structure
const validateResponse = (completion) => {
    if (!completion) {
        throw new Error('No response received');
    }

    if (!completion.choices || completion.choices.length === 0) {
        throw new Error('No choices in response');
    }

    if (!completion.choices[0].message) {
        throw new Error('No message in response');
    }

    return completion.choices[0].message.content;
};

// Safe response handling
const safeRequest = async (messages) => {
    try {
        const completion = await client.chat.completions.create({
            model: 'gpt-4o',
            messages: messages
        });

        return validateResponse(completion);
    } catch (error) {
        console.error('Request failed:', error.message);
        return null;
    }
};
```

### Debug Mode

Enable detailed logging for troubleshooting:

```javascript
// Enable debug logging
process.env.DEBUG = 'openai:*';

// Custom debug logger
class DebugLiteLLMClient {
    constructor() {
        this.client = new OpenAI({
            apiKey: process.env.LITELLM_API_KEY,
            baseURL: 'https://ai.gateway.equalcollective.com/v1'
        });
    }

    async debugRequest(requestBody) {
        console.log('🔍 Debug Request:');
        console.log('URL:', 'https://ai.gateway.equalcollective.com/v1/chat/completions');
        console.log('Headers:', {
            'Authorization': `Bearer ${process.env.LITELLM_API_KEY?.substring(0, 10)}...`,
            'Content-Type': 'application/json'
        });
        console.log('Body:', JSON.stringify(requestBody, null, 2));

        const startTime = Date.now();

        try {
            const response = await this.client.chat.completions.create(requestBody);
            const duration = Date.now() - startTime;

            console.log('✅ Debug Response:');
            console.log('Duration:', `${duration}ms`);
            console.log('Model:', response.model);
            console.log('Usage:', response.usage);
            console.log('Response:', response.choices[0]?.message?.content?.substring(0, 100) + '...');

            return response;
        } catch (error) {
            const duration = Date.now() - startTime;

            console.log('❌ Debug Error:');
            console.log('Duration:', `${duration}ms`);
            console.log('Status:', error.status);
            console.log('Message:', error.message);
            console.log('Details:', error.error);

            throw error;
        }
    }
}
```

## Documentation and Resources

### Official LiteLLM Documentation

For comprehensive LiteLLM documentation, visit:
**[https://ai.gateway.equalcollective.com/](https://ai.gateway.equalcollective.com/)**

### Quick Reference

#### Environment Variables
```bash
LITELLM_API_KEY=sk-your-persona-key-here
LITELLM_PROXY_URL=https://ai.gateway.equalcollective.com
```

#### Basic Setup
```javascript
const OpenAI = require('openai');
const client = new OpenAI({
    apiKey: process.env.LITELLM_API_KEY,
    baseURL: 'https://ai.gateway.equalcollective.com/v1'
});
```

#### Request with Tags
```javascript
const completion = await client.chat.completions.create({
    model: 'gpt-4o',
    messages: [{ role: 'user', content: 'Hello' }],
    user: 'your_user_id',
    metadata: {
        tags: ['service:YourService', 'function:yourFunction']
    }
});
```

### Support

For technical support or questions about the LiteLLM gateway integration:
1. Check this documentation first
2. Run the test suite to identify issues
3. Contact your system administrator
4. Refer to the official documentation at the gateway URL
```
```
