# Dashboard Queries for 5D Analytics System

## How to Use These Queries in Metabase

1. Create a new Question in Metabase
2. Choose "Native query" 
3. Paste the SQL
4. Save and add to dashboard
5. Configure dashboard filters to map to query variables

## Dashboard Filters Setup

Create these 5 filters on your dashboard:
1. **Client** - Text filter → Maps to `{{client}}`
2. **Funnel** - Text filter → Maps to `{{funnel}}`
3. **SP** - Text filter → Maps to `{{sp}}`
4. **Email Provider** - Text filter → Maps to `{{provider}}`
5. **Domain Type** - Text filter → Maps to `{{domain}}`

## Query Templates

### 1. Main Overview Card
```sql
SELECT 
  COUNT(DISTINCT client) as "Active Clients",
  COUNT(DISTINCT funnel) as "Active Funnels",
  SUM(email_1_sent_count + email_2_sent_count + email_3_sent_count) as "Total Emails Sent",
  <PERSON>UM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3) as "Total Replies",
  SUM(meetings_booked) as "Meetings Booked",
  ROUND(
    SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3)::numeric / 
    NULLIF(SUM(email_1_sent_count + email_2_sent_count + email_3_sent_count), 0) * 100, 
    2
  ) as "Overall Reply Rate %"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]
```

### 2. Email Sequence Performance
```sql
SELECT 
  'Email 1' as "Sequence",
  SUM(email_1_sent_count) as "Sent",
  SUM(email_1_sent_personal) as "Personal",
  SUM(email_1_sent_role) as "Role",
  SUM(email_1_sent_work) as "Work",
  SUM(email_1_sent_unknown) as "Unknown",
  SUM(replies_after_email_1) as "Replies",
  SUM(automated_replies_after_email_1) as "Auto Replies",
  SUM(error_replies_after_email_1) as "Errors",
  ROUND(SUM(replies_after_email_1)::numeric / NULLIF(SUM(email_1_sent_count), 0) * 100, 2) as "Reply Rate %"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]

UNION ALL

SELECT 
  'Email 2' as "Sequence",
  SUM(email_2_sent_count),
  SUM(email_2_sent_personal),
  SUM(email_2_sent_role),
  SUM(email_2_sent_work),
  SUM(email_2_sent_unknown),
  SUM(replies_after_email_2),
  SUM(automated_replies_after_email_2),
  SUM(error_replies_after_email_2),
  ROUND(SUM(replies_after_email_2)::numeric / NULLIF(SUM(email_2_sent_count), 0) * 100, 2)
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]

UNION ALL

SELECT 
  'Email 3' as "Sequence",
  SUM(email_3_sent_count),
  SUM(email_3_sent_personal),
  SUM(email_3_sent_role),
  SUM(email_3_sent_work),
  SUM(email_3_sent_unknown),
  SUM(replies_after_email_3),
  SUM(automated_replies_after_email_3),
  SUM(error_replies_after_email_3),
  ROUND(SUM(replies_after_email_3)::numeric / NULLIF(SUM(email_3_sent_count), 0) * 100, 2)
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]

ORDER BY "Sequence"
```

### 3. SP Performance Breakdown
```sql
SELECT 
  sp as "Search Priority",
  SUM(email_1_sent_count) as "Email 1 Sent",
  SUM(replies_after_email_1) as "Email 1 Replies",
  ROUND(SUM(replies_after_email_1)::numeric / NULLIF(SUM(email_1_sent_count), 0) * 100, 2) as "Email 1 Reply %",
  SUM(email_2_sent_count) as "Email 2 Sent",
  SUM(replies_after_email_2) as "Email 2 Replies",
  ROUND(SUM(replies_after_email_2)::numeric / NULLIF(SUM(email_2_sent_count), 0) * 100, 2) as "Email 2 Reply %",
  SUM(email_3_sent_count) as "Email 3 Sent",
  SUM(replies_after_email_3) as "Email 3 Replies",
  ROUND(SUM(replies_after_email_3)::numeric / NULLIF(SUM(email_3_sent_count), 0) * 100, 2) as "Email 3 Reply %",
  SUM(meetings_booked) as "Meetings"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]
GROUP BY sp
ORDER BY sp
```

### 4. Provider Performance
```sql
SELECT 
  email_provider as "Email Provider",
  COUNT(DISTINCT client) as "Clients",
  SUM(email_1_sent_count + email_2_sent_count + email_3_sent_count) as "Total Sent",
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3) as "Total Replies",
  SUM(automated_replies_after_email_1 + automated_replies_after_email_2 + automated_replies_after_email_3) as "Auto Replies",
  SUM(error_replies_after_email_1 + error_replies_after_email_2 + error_replies_after_email_3) as "Errors",
  ROUND(
    SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3)::numeric / 
    NULLIF(SUM(email_1_sent_count + email_2_sent_count + email_3_sent_count), 0) * 100, 
    2
  ) as "Reply Rate %",
  ROUND(
    SUM(error_replies_after_email_1 + error_replies_after_email_2 + error_replies_after_email_3)::numeric / 
    NULLIF(SUM(email_1_sent_count + email_2_sent_count + email_3_sent_count), 0) * 100, 
    2
  ) as "Error Rate %"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]
GROUP BY email_provider
ORDER BY "Total Sent" DESC
```

### 5. Domain Type Analysis
```sql
SELECT 
  domain_type as "Domain Type",
  COUNT(DISTINCT email_provider) as "Providers",
  SUM(email_1_sent_count + email_2_sent_count + email_3_sent_count) as "Total Sent",
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3) as "Total Replies",
  SUM(error_replies_after_email_1 + error_replies_after_email_2 + error_replies_after_email_3) as "Total Errors",
  ROUND(
    SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3)::numeric / 
    NULLIF(SUM(email_1_sent_count + email_2_sent_count + email_3_sent_count), 0) * 100, 
    2
  ) as "Reply Rate %",
  ROUND(
    SUM(error_replies_after_email_1 + error_replies_after_email_2 + error_replies_after_email_3)::numeric / 
    NULLIF(SUM(email_1_sent_count + email_2_sent_count + email_3_sent_count), 0) * 100, 
    2
  ) as "Error Rate %"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]
GROUP BY domain_type
ORDER BY "Total Sent" DESC
```

### 6. Weekly Trend Chart
```sql
SELECT 
  week_start_date as "Week",
  SUM(email_1_sent_count) as "Email 1",
  SUM(email_2_sent_count) as "Email 2",
  SUM(email_3_sent_count) as "Email 3",
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3) as "Total Replies",
  SUM(meetings_booked) as "Meetings Booked"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '90 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]
GROUP BY week_start_date
ORDER BY week_start_date
```

### 7. Client Performance Table
```sql
SELECT 
  client as "Client",
  COUNT(DISTINCT funnel) as "Funnels",
  COUNT(DISTINCT sp) as "SPs Used",
  SUM(total_prospects_reached) as "Prospects",
  SUM(email_1_sent_count + email_2_sent_count + email_3_sent_count) as "Emails Sent",
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3) as "Replies",
  SUM(meetings_booked) as "Meetings",
  ROUND(
    SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3)::numeric / 
    NULLIF(SUM(email_1_sent_count + email_2_sent_count + email_3_sent_count), 0) * 100, 
    2
  ) as "Reply Rate %"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]
GROUP BY client
ORDER BY "Emails Sent" DESC
LIMIT 20
```

### 8. Funnel Performance Comparison
```sql
SELECT 
  funnel as "Funnel Code",
  COUNT(DISTINCT client) as "Active Clients",
  SUM(total_prospects_reached) as "Prospects",
  SUM(email_1_sent_count) as "Email 1",
  SUM(email_2_sent_count) as "Email 2",
  SUM(email_3_sent_count) as "Email 3",
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3) as "Total Replies",
  SUM(meetings_booked) as "Meetings",
  ROUND(
    SUM(meetings_booked)::numeric / 
    NULLIF(SUM(total_prospects_reached), 0) * 100, 
    2
  ) as "Meeting Rate %"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]
GROUP BY funnel
ORDER BY "Prospects" DESC
```

### 9. Meeting Attribution
```sql
SELECT 
  'After Email 1' as "Attribution",
  SUM(meeting_slots_sent_after_1) as "Slots Sent",
  ROUND(
    SUM(meeting_slots_sent_after_1)::numeric / 
    NULLIF(SUM(meeting_slots_sent_after_1 + meeting_slots_sent_after_2 + meeting_slots_sent_after_3 + meeting_slots_sent_unknown), 0) * 100,
    1
  ) as "% of Total"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]

UNION ALL

SELECT 
  'After Email 2',
  SUM(meeting_slots_sent_after_2),
  ROUND(
    SUM(meeting_slots_sent_after_2)::numeric / 
    NULLIF(SUM(meeting_slots_sent_after_1 + meeting_slots_sent_after_2 + meeting_slots_sent_after_3 + meeting_slots_sent_unknown), 0) * 100,
    1
  )
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]

UNION ALL

SELECT 
  'After Email 3',
  SUM(meeting_slots_sent_after_3),
  ROUND(
    SUM(meeting_slots_sent_after_3)::numeric / 
    NULLIF(SUM(meeting_slots_sent_after_1 + meeting_slots_sent_after_2 + meeting_slots_sent_after_3 + meeting_slots_sent_unknown), 0) * 100,
    1
  )
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]

UNION ALL

SELECT 
  'Unknown',
  SUM(meeting_slots_sent_unknown),
  ROUND(
    SUM(meeting_slots_sent_unknown)::numeric / 
    NULLIF(SUM(meeting_slots_sent_after_1 + meeting_slots_sent_after_2 + meeting_slots_sent_after_3 + meeting_slots_sent_unknown), 0) * 100,
    1
  )
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]

ORDER BY "Attribution"
```

### 10. Prospect Email Type Performance
```sql
SELECT 
  'Personal' as "Email Type",
  SUM(email_1_sent_personal + email_2_sent_personal + email_3_sent_personal) as "Total Sent",
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3) as "Replies"
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  AND (email_1_sent_personal > 0 OR email_2_sent_personal > 0 OR email_3_sent_personal > 0)
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]

UNION ALL

SELECT 
  'Role',
  SUM(email_1_sent_role + email_2_sent_role + email_3_sent_role),
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3)
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  AND (email_1_sent_role > 0 OR email_2_sent_role > 0 OR email_3_sent_role > 0)
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]

UNION ALL

SELECT 
  'Work',
  SUM(email_1_sent_work + email_2_sent_work + email_3_sent_work),
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3)
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  AND (email_1_sent_work > 0 OR email_2_sent_work > 0 OR email_3_sent_work > 0)
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]

UNION ALL

SELECT 
  'Unknown',
  SUM(email_1_sent_unknown + email_2_sent_unknown + email_3_sent_unknown),
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3)
FROM public2.analytics_5_dimensions
WHERE week_start_date >= CURRENT_DATE - INTERVAL '30 days'
  AND (email_1_sent_unknown > 0 OR email_2_sent_unknown > 0 OR email_3_sent_unknown > 0)
  [[AND client = {{client}}]]
  [[AND funnel = {{funnel}}]]
  [[AND sp = {{sp}}]]
  [[AND email_provider = {{provider}}]]
  [[AND domain_type = {{domain}}]]

ORDER BY "Total Sent" DESC
```

## Notes on Filter Syntax

- `[[AND field = {{variable}}]]` - This is Metabase's optional filter syntax
- When a filter is not selected, the entire clause is removed
- When a filter is selected, it adds the AND condition
- This allows all queries to work with any combination of filters

## Creating the Dashboard

1. Create a new dashboard called "5D Analytics Dashboard"
2. Add the 5 filters at the top (Client, Funnel, SP, Provider, Domain)
3. Create each query as a separate card
4. Link each card's filters to the dashboard filters
5. Arrange cards in a logical layout:
   - Overview metrics at top
   - Charts in middle
   - Detailed tables at bottom

## Performance Tips

1. All queries use the indexed `analytics_5_dimensions` table
2. Filters reduce data scanned, improving performance
3. Date ranges are important - adjust based on your needs
4. Consider creating saved views for commonly used filter combinations